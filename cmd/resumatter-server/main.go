package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"resumatter/internal/server"
	"resumatter/pkg/config"
	"resumatter/pkg/logger"
	"resumatter/pkg/observability"
	"resumatter/pkg/service"
)

func main() {
	// Load configuration
	cfg := config.Load()
	if err := cfg.Validate(); err != nil {
		fmt.Fprintf(os.Stderr, "Configuration error: %v\n", err)
		os.Exit(1)
	}

	// Create logger
	log := logger.NewDefault().Named("resumatter-server")

	// Create observability provider
	obsConfig := observability.LoadConfigFromEnv()
	obsConfig.ServiceName = "resumatter-server"
	obsConfig.MetricsEnabled = true
	obsConfig.MetricsExporter = observability.MetricsExporterJSONFile
	obsConfig.MetricsFilePath = "server-metrics.json"

	obsProvider, err := observability.NewProvider(obsConfig)
	if err != nil {
		log.ErrorWithErr(context.Background(), "Failed to create observability provider", err)
		os.Exit(1)
	}
	defer obsProvider.Shutdown(context.Background())

	// Create service
	svc, err := service.New(
		service.WithConfig(cfg),
		service.WithLogger(log),
		service.WithObservability(obsProvider),
	)
	if err != nil {
		log.ErrorWithErr(context.Background(), "Failed to create service", err)
		os.Exit(1)
	}
	defer svc.Close()

	// Create HTTP server
	serverConfig := server.DefaultConfig()
	if port := os.Getenv("PORT"); port != "" {
		serverConfig.Port = port
	}
	if env := os.Getenv("ENVIRONMENT"); env != "" {
		serverConfig.Environment = env
	}

	httpServer := server.NewServer(serverConfig, svc)

	// Start server in a goroutine
	go func() {
		log.Info(context.Background(), "Starting Resumatter HTTP server",
			logger.String("port", serverConfig.Port))

		if err := httpServer.Start(); err != nil {
			log.ErrorWithErr(context.Background(), "HTTP server failed", err)
			os.Exit(1)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info(context.Background(), "Shutting down server...")

	// Give the server 30 seconds to shutdown gracefully
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := httpServer.Shutdown(ctx); err != nil {
		log.ErrorWithErr(context.Background(), "Server forced to shutdown", err)
		os.Exit(1)
	}

	log.Info(context.Background(), "Server exited")
}
