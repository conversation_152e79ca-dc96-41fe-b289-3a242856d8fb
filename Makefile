.PHONY: build clean test run-example help lint fmt vet

# Build configuration
BINARY_NAME = resumatter
BUILD_DIR = ./build
CMD_DIR = ./cmd/resumatter

# Shell function for API key check
check_api_key = \
	if [ -z "$$GEMINI_API_KEY" ] && [ -z "$$RESUMATTER_AI_APIKEY" ]; then \
		echo "Error: Please set GEMINI_API_KEY or RESUMATTER_AI_APIKEY environment variable"; \
		exit 1; \
	fi

# Default target
all: build

# Build the application
build: deps
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	go build -o $(BUILD_DIR)/$(BINARY_NAME) $(CMD_DIR)
	@echo "Build complete: $(BUILD_DIR)/$(BINARY_NAME)"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR)
	rm -f coverage.*
	rm -f *.out
	go clean

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	go mod tidy

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Vet code
vet:
	@echo "Vetting code..."
	go vet ./...

# Lint code (requires golangci-lint)
lint:
	@echo "Linting code..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not found, skipping lint check"; \
	fi

# Run fast unit tests (no API calls)
test-fast:
	@echo "Running fast unit tests..."
	go test -tags=fast -v ./...

# Run integration tests (requires API key)
test-integration:
	@echo "Running integration tests..."
	@if [ -z "$$GEMINI_API_KEY" ] && [ -z "$$RESUMATTER_AI_APIKEY" ]; then \
		echo "Warning: No API key set. Integration tests may be skipped."; \
	fi
	go test -tags=integration -v ./...

# Run all tests (fast + integration)
test:
	@echo "Running all tests..."
	@$(MAKE) test-fast
	@$(MAKE) test-integration

# Run fast tests with coverage
test-coverage: deps
	@echo "Running fast tests with coverage..."
	go test -tags=fast -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.out, coverage.html"

# Run integration tests with coverage
test-coverage-integration: deps
	@echo "Running integration tests with coverage..."
	go test -tags=integration -v -coverprofile=coverage-integration.out ./...
	go tool cover -html=coverage-integration.out -o coverage-integration.html
	@echo "Integration coverage report generated: coverage-integration.out, coverage-integration.html"

# Run example tailor command (requires API key)
run-example:
	@echo "Running example tailor command..."
	@$(check_api_key)
	go run $(CMD_DIR) tailor examples/resume.txt examples/job.txt

# Run example with JSON output
run-example-json:
	@echo "Running example with JSON output..."
	@$(check_api_key)
	go run $(CMD_DIR) tailor examples/resume.txt examples/job.txt --format json

# Run evaluation example
run-evaluate:
	@echo "Running evaluation example..."
	@$(check_api_key)
	go run $(CMD_DIR) evaluate examples/resume.txt examples/resume.txt

# Run analysis example
run-analyze:
	@echo "Running analysis example..."
	@$(check_api_key)
	go run $(CMD_DIR) analyze examples/job.txt

# Install the binary to $GOPATH/bin
install: build
	@echo "Installing $(BINARY_NAME) to $$GOPATH/bin..."
	cp $(BUILD_DIR)/$(BINARY_NAME) $$GOPATH/bin/

# Development workflow (fast tests only for quick feedback)
dev: fmt vet lint test-fast

# Show help
help:
	@echo "Available targets:"
	@echo "  build                    - Build the application"
	@echo "  clean                    - Clean build artifacts"
	@echo "  deps                     - Download and tidy dependencies"
	@echo "  fmt                      - Format code"
	@echo "  vet                      - Vet code"
	@echo "  lint                     - Lint code (requires golangci-lint)"
	@echo "  test                     - Run all tests (fast + integration)"
	@echo "  test-fast                - Run fast unit tests (no API calls)"
	@echo "  test-integration         - Run integration tests (requires API key)"
	@echo "  test-coverage            - Run fast tests with coverage"
	@echo "  test-coverage-integration - Run integration tests with coverage"
	@echo "  run-example              - Run example tailor command"
	@echo "  run-example-json         - Run example with JSON output"
	@echo "  run-evaluate             - Run evaluation example"
	@echo "  run-analyze              - Run analysis example"
	@echo "  install                  - Install binary to GOPATH/bin"
	@echo "  dev                      - Run development workflow (fmt, vet, lint, test-fast)"
	@echo "  help                     - Show this help message"
	@echo ""
	@echo "Testing Strategy:"
	@echo "  test-fast        - Quick unit tests, no external dependencies"
	@echo "  test-integration - Slower tests that require valid API key"
	@echo ""
	@echo "Environment variables:"
	@echo "  GEMINI_API_KEY or RESUMATTER_AI_APIKEY - Required for API access"
	@echo "  GEMINI_MODEL - Optional, defaults to gemini-2.0-flash-lite"

