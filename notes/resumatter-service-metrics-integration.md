```go
// Example of how metrics would be integrated into the service layer
// This shows the practical implementation in the existing codebase

package service

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/attribute"

	"resumatter/pkg/ai"
	"resumatter/pkg/config"
	"resumatter/pkg/fileio"
	"resumatter/pkg/formatter"
	"resumatter/pkg/logger"
	"resumatter/pkg/observability"
	"resumatter/pkg/types"
)

// Enhanced Service with metrics support
type EnhancedService struct {
	aiClient              ai.ClientInterface
	reader                *fileio.Reader
	writer                *fileio.Writer
	formatter             *formatter.Formatter
	logger                logger.Logger
	observabilityProvider observability.ProviderInterface
	middleware            observability.MiddlewareInterface
	metricsCollector      *observability.MetricsCollector // New
}

// NewEnhancedService creates a new service instance with metrics support
func NewEnhancedService(opts ...ServiceOption) (*EnhancedService, error) {
	service := &EnhancedService{}

	// Apply all options (reuse existing option pattern)
	for _, opt := range opts {
		if err := opt(service); err != nil {
			return nil, fmt.Errorf("failed to apply service option: %w", err)
		}
	}

	// Validate required components (same as before)
	if service.aiClient == nil {
		return nil, fmt.Errorf("AI client is required but not provided")
	}
	// ... other validations

	// Set up observability middleware and metrics collector
	if service.observabilityProvider != nil {
		if provider, ok := service.observabilityProvider.(*observability.EnhancedProvider); ok {
			service.middleware = observability.NewServiceMiddleware(provider.Provider)
			service.metricsCollector = observability.NewMetricsCollector(provider)
		}
	}

	return service, nil
}

// TailorResume with comprehensive metrics collection
func (s *EnhancedService) TailorResume(ctx context.Context, opts ...TailorResumeOption) (*types.TailorResumeOutput, error) {
	start := time.Now()
	
	// Increment concurrent operations
	if s.metricsCollector != nil {
		s.metricsCollector.IncrementConcurrentOperations(ctx, 
			attribute.String("operation", "tailor_resume"))
		defer s.metricsCollector.DecrementConcurrentOperations(ctx,
			attribute.String("operation", "tailor_resume"))
		
		// Increment total requests
		s.metricsCollector.IncrementRequests(ctx, "tailor_resume")
	}

	// Add logger to context for downstream components
	ctx = logger.WithLogger(ctx, s.logger)

	// Use observability middleware if available, otherwise execute directly
	if s.middleware != nil {
		result, err := s.middleware.WrapOperationWithResult(ctx, "tailor-resume", func(ctx context.Context) (any, error) {
			return s.tailorResumeInternal(ctx, opts...)
		})
		
		// Record metrics based on result
		s.recordTailorMetrics(ctx, start, result, err)
		
		if err != nil {
			return nil, err
		}
		return result.(*types.TailorResumeOutput), nil
	}

	result, err := s.tailorResumeInternal(ctx, opts...)
	s.recordTailorMetrics(ctx, start, result, err)
	return result, err
}

// recordTailorMetrics records metrics for tailor operation
func (s *EnhancedService) recordTailorMetrics(ctx context.Context, start time.Time, result any, err error) {
	if s.metricsCollector == nil {
		return
	}

	duration := time.Since(start)
	
	// Record operation duration
	s.metricsCollector.RecordOperationDuration(ctx, "tailor_resume", duration)

	if err != nil {
		// Record failed request
		errorType := "unknown"
		if _, ok := err.(*ai.APIError); ok {
			errorType = "ai_api_error"
		} else if _, ok := err.(*config.ValidationError); ok {
			errorType = "validation_error"
		}
		
		s.metricsCollector.IncrementFailedRequests(ctx, "tailor_resume", errorType)
		return
	}

	// Record successful operation
	s.metricsCollector.IncrementResumesTailored(ctx)

	// Record business metrics if we have the result
	if tailorResult, ok := result.(*types.TailorResumeOutput); ok {
		// Record ATS score
		s.metricsCollector.RecordATSScore(ctx, tailorResult.ATSAnalysis.Score)
		
		// Record content metrics with attributes
		s.metricsCollector.RecordContentLength(ctx, "tailored_resume", 
			len(tailorResult.TailoredResume),
			attribute.String("content_type", "tailored_resume"))
	}
}

// EvaluateResume with metrics
func (s *EnhancedService) EvaluateResume(ctx context.Context, opts ...EvaluateResumeOption) (*types.EvaluateResumeOutput, error) {
	start := time.Now()
	
	if s.metricsCollector != nil {
		s.metricsCollector.IncrementConcurrentOperations(ctx,
			attribute.String("operation", "evaluate_resume"))
		defer s.metricsCollector.DecrementConcurrentOperations(ctx,
			attribute.String("operation", "evaluate_resume"))
		
		s.metricsCollector.IncrementRequests(ctx, "evaluate_resume")
	}

	ctx = logger.WithLogger(ctx, s.logger)

	if s.middleware != nil {
		result, err := s.middleware.WrapOperationWithResult(ctx, "evaluate-resume", func(ctx context.Context) (any, error) {
			return s.evaluateResumeInternal(ctx, opts...)
		})
		
		s.recordEvaluateMetrics(ctx, start, result, err)
		
		if err != nil {
			return nil, err
		}
		return result.(*types.EvaluateResumeOutput), nil
	}

	result, err := s.evaluateResumeInternal(ctx, opts...)
	s.recordEvaluateMetrics(ctx, start, result, err)
	return result, err
}

// recordEvaluateMetrics records metrics for evaluate operation
func (s *EnhancedService) recordEvaluateMetrics(ctx context.Context, start time.Time, result any, err error) {
	if s.metricsCollector == nil {
		return
	}

	duration := time.Since(start)
	s.metricsCollector.RecordOperationDuration(ctx, "evaluate_resume", duration)

	if err != nil {
		errorType := "unknown"
		if _, ok := err.(*ai.APIError); ok {
			errorType = "ai_api_error"
		}
		s.metricsCollector.IncrementFailedRequests(ctx, "evaluate_resume", errorType)
		return
	}

	// Record successful operation
	s.metricsCollector.IncrementResumesEvaluated(ctx)

	// Record integrity findings
	if evalResult, ok := result.(*types.EvaluateResumeOutput); ok {
		// Count findings by type
		findingCounts := make(map[string]int)
		for _, finding := range evalResult.Findings {
			findingCounts[finding.Type]++
		}
		
		// Record each finding type
		for findingType, count := range findingCounts {
			s.metricsCollector.IncrementIntegrityIssues(ctx, findingType, count)
		}
		
		// Record total findings
		if len(evalResult.Findings) > 0 {
			s.metricsCollector.RecordFindingsPerEvaluation(ctx, len(evalResult.Findings))
		}
	}
}

// AnalyzeJob with metrics
func (s *EnhancedService) AnalyzeJob(ctx context.Context, opts ...AnalyzeJobOption) (*types.AnalyzeJobOutput, error) {
	start := time.Now()
	
	if s.metricsCollector != nil {
		s.metricsCollector.IncrementConcurrentOperations(ctx,
			attribute.String("operation", "analyze_job"))
		defer s.metricsCollector.DecrementConcurrentOperations(ctx,
			attribute.String("operation", "analyze_job"))
		
		s.metricsCollector.IncrementRequests(ctx, "analyze_job")
	}

	ctx = logger.WithLogger(ctx, s.logger)

	if s.middleware != nil {
		result, err := s.middleware.WrapOperationWithResult(ctx, "analyze-job", func(ctx context.Context) (any, error) {
			return s.analyzeJobInternal(ctx, opts...)
		})
		
		s.recordAnalyzeMetrics(ctx, start, result, err)
		
		if err != nil {
			return nil, err
		}
		return result.(*types.AnalyzeJobOutput), nil
	}

	result, err := s.analyzeJobInternal(ctx, opts...)
	s.recordAnalyzeMetrics(ctx, start, result, err)
	return result, err
}

// recordAnalyzeMetrics records metrics for analyze operation
func (s *EnhancedService) recordAnalyzeMetrics(ctx context.Context, start time.Time, result any, err error) {
	if s.metricsCollector == nil {
		return
	}

	duration := time.Since(start)
	s.metricsCollector.RecordOperationDuration(ctx, "analyze_job", duration)

	if err != nil {
		errorType := "unknown"
		if _, ok := err.(*ai.APIError); ok {
			errorType = "ai_api_error"
		}
		s.metricsCollector.IncrementFailedRequests(ctx, "analyze_job", errorType)
		return
	}

	// Record successful operation
	s.metricsCollector.IncrementJobsAnalyzed(ctx)

	// Record quality scores
	if analyzeResult, ok := result.(*types.AnalyzeJobOutput); ok {
		s.metricsCollector.RecordJobQualityScore(ctx, analyzeResult.JobQualityScore)
		
		// Record detailed scores with attributes
		s.metricsCollector.RecordQualityScore(ctx, "clarity", analyzeResult.Clarity.Score)
		s.metricsCollector.RecordQualityScore(ctx, "inclusivity", analyzeResult.Inclusivity.Score)
		s.metricsCollector.RecordQualityScore(ctx, "candidate_attraction", analyzeResult.CandidateAttraction.Score)
	}
}

// Enhanced AI Client with metrics
type EnhancedAIClient struct {
	*ai.Client // Embed existing client
	metricsCollector *observability.MetricsCollector
	config *config.Config
}

// TailorResume with AI metrics
func (c *EnhancedAIClient) TailorResume(ctx context.Context, input types.TailorResumeInput) (*types.TailorResumeOutput, error) {
	start := time.Now()
	
	if c.metricsCollector != nil {
		c.metricsCollector.IncrementAIRequests(ctx, "tailor", c.config.Model)
	}

	// Call original method
	result, err := c.Client.TailorResume(ctx, input)
	
	// Record metrics
	if c.metricsCollector != nil {
		duration := time.Since(start)
		c.metricsCollector.RecordAIRequestDuration(ctx, "tailor", duration)
		
		if err != nil {
			errorType := "api_error"
			if isRateLimitError(err) {
				errorType = "rate_limit"
			} else if isAuthError(err) {
				errorType = "auth_error"
			}
			c.metricsCollector.IncrementAIErrors(ctx, "tailor", errorType)
		} else {
			// Estimate response size (in a real implementation, you'd get this from the API)
			responseSize := len(result.TailoredResume) + len(result.ATSAnalysis.Strengths) + len(result.ATSAnalysis.Weaknesses)
			c.metricsCollector.RecordAIResponseSize(ctx, "tailor", responseSize)
			
			// If we had token usage information from the API, we'd record it here
			// c.metricsCollector.IncrementAITokensConsumed(ctx, tokenCount, c.config.Model)
		}
	}

	return result, err
}

// Configuration example for metrics
func ExampleMetricsConfiguration() {
	// Environment-based configuration
	config := &observability.EnhancedConfig{
		Config: &observability.Config{
			ServiceName:    "resumatter",
			ServiceVersion: "1.0.0",
			Environment:    "production",
			TracingEnabled: true,
			TraceExporter:  observability.TraceExporterStdout,
		},
		MetricsEnabled:  true,
		MetricsExporter: observability.MetricsExporterPrometheus,
		MetricsInterval: 30 * time.Second,
	}

	// Create enhanced provider
	provider, err := observability.NewEnhancedProvider(config)
	if err != nil {
		panic(err)
	}
	defer provider.Shutdown(context.Background())

	// Create service with metrics
	service, err := NewEnhancedService(
		WithConfig(config.Config),
		WithObservability(provider),
	)
	if err != nil {
		panic(err)
	}
	defer service.Close()

	// Use service normally - metrics are collected automatically
	ctx := context.Background()
	result, err := service.TailorResume(ctx, 
		WithResumeData("resume content"),
		WithJobDescriptionData("job description"))
	
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("ATS Score: %d\n", result.ATSAnalysis.Score)
	}
}

// Helper functions for error classification
func isRateLimitError(err error) bool {
	// Implementation would check for specific error types
	return false
}

func isAuthError(err error) bool {
	// Implementation would check for authentication errors
	return false
}

// Additional metrics methods that would be added to MetricsCollector
func (m *MetricsCollector) RecordContentLength(ctx context.Context, contentType string, length int, attributes ...attribute.KeyValue) {
	// This would use a histogram to track content lengths
	// Implementation would be similar to other histogram methods
}

func (m *MetricsCollector) RecordFindingsPerEvaluation(ctx context.Context, count int, attributes ...attribute.KeyValue) {
	// This would track the distribution of findings per evaluation
	// Implementation would use a histogram
}

func (m *MetricsCollector) RecordQualityScore(ctx context.Context, scoreType string, score int, attributes ...attribute.KeyValue) {
	// This would track different quality score types
	attrs := append(attributes, attribute.String("score_type", scoreType))
	// Implementation would use the appropriate histogram
}
```