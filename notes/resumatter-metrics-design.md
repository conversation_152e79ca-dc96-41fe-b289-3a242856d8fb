# Metrics Implementation Design for Resumatter

## Overview
This document outlines the design for implementing OpenTelemetry metrics in the Resumatter application, building on the existing observability framework.

## Metrics Categories

### 1. Business Metrics
- **Resume Operations**
  - `resumatter_resumes_tailored_total` (Counter) - Total resumes tailored
  - `resumatter_resumes_evaluated_total` (Counter) - Total resumes evaluated
  - `resumatter_jobs_analyzed_total` (Counter) - Total job descriptions analyzed
  - `resumatter_ats_score` (Histogram) - Distribution of ATS scores
  - `resumatter_job_quality_score` (Histogram) - Distribution of job quality scores

### 2. Performance Metrics
- **Operation Duration**
  - `resumatter_operation_duration_seconds` (Histogram) - Duration of operations
  - `resumatter_ai_request_duration_seconds` (Histogram) - AI API call duration
  - `resumatter_file_processing_duration_seconds` (Histogram) - File I/O duration

### 3. System Metrics
- **Request Metrics**
  - `resumatter_requests_total` (Counter) - Total requests by operation
  - `resumatter_requests_failed_total` (Counter) - Failed requests by operation
  - `resumatter_concurrent_operations` (UpDownCounter) - Active operations

### 4. AI Service Metrics
- **AI Performance**
  - `resumatter_ai_tokens_consumed_total` (Counter) - Total AI tokens used
  - `resumatter_ai_requests_total` (Counter) - Total AI API calls
  - `resumatter_ai_errors_total` (Counter) - AI API errors
  - `resumatter_ai_response_size_bytes` (Histogram) - AI response sizes

### 5. Quality Metrics
- **Content Analysis**
  - `resumatter_integrity_issues_total` (Counter) - Resume integrity issues found
  - `resumatter_content_length_chars` (Histogram) - Input content lengths
  - `resumatter_findings_per_evaluation` (Histogram) - Issues found per evaluation

## Implementation Structure

### Enhanced Observability Provider
```go
type Provider struct {
    config         *Config
    tracerProvider *trace.TracerProvider
    meterProvider  *metric.MeterProvider  // New
    logger         logger.Logger
    
    // Metrics instruments
    meters map[string]metric.Meter
    counters map[string]metric.Int64Counter
    histograms map[string]metric.Float64Histogram
    upDownCounters map[string]metric.Int64UpDownCounter
}
```

### Metrics Middleware
```go
type MetricsMiddleware struct {
    provider *Provider
    
    // Business metrics
    resumesTailoredCounter    metric.Int64Counter
    resumesEvaluatedCounter   metric.Int64Counter
    jobsAnalyzedCounter       metric.Int64Counter
    
    // Performance metrics
    operationDurationHist     metric.Float64Histogram
    aiRequestDurationHist     metric.Float64Histogram
    
    // Quality metrics
    atsScoreHist             metric.Float64Histogram
    jobQualityScoreHist      metric.Float64Histogram
}
```

## Configuration

### Environment Variables
```bash
# Enable metrics
OTEL_METRICS_ENABLED=true

# Metrics exporter (future)
OTEL_METRICS_EXPORTER=prometheus  # or "stdout", "otlp"

# Metrics collection interval
OTEL_METRICS_INTERVAL=30s

# Custom metrics configuration
RESUMATTER_METRICS_DETAILED=true  # Enable detailed histograms
```

### Programmatic Configuration
```go
config := observability.NewConfig(
    observability.WithServiceInfo("resumatter", "1.0.0", "production"),
    observability.WithTracing(observability.TraceExporterStdout),
    observability.WithMetrics(observability.MetricsExporterPrometheus),
    observability.WithMetricsInterval(30*time.Second),
)
```

## Integration Points

### Service Layer Integration
```go
func (s *Service) TailorResume(ctx context.Context, opts ...TailorResumeOption) (*types.TailorResumeOutput, error) {
    // Increment operation counter
    if s.metricsMiddleware != nil {
        s.metricsMiddleware.IncrementOperation("tailor_resume")
        defer s.metricsMiddleware.RecordOperationDuration("tailor_resume", time.Now())
    }
    
    // Existing logic...
    result, err := s.tailorResumeInternal(ctx, opts...)
    
    if err == nil && s.metricsMiddleware != nil {
        // Record business metrics
        s.metricsMiddleware.RecordATSScore(result.ATSAnalysis.Score)
        s.metricsMiddleware.RecordContentLength("resume", len(input.BaseResume))
    }
    
    return result, err
}
```

### AI Client Integration
```go
func (c *Client) TailorResume(ctx context.Context, input types.TailorResumeInput) (*types.TailorResumeOutput, error) {
    start := time.Now()
    
    // Record AI request
    if c.metricsCollector != nil {
        c.metricsCollector.IncrementAIRequest("tailor")
        defer c.metricsCollector.RecordAIRequestDuration("tailor", start)
    }
    
    result, err := c.generateJSON(ctx, systemPrompt, userPrompt, schema, &output)
    
    if c.metricsCollector != nil {
        if err != nil {
            c.metricsCollector.IncrementAIError("tailor", err)
        } else {
            c.metricsCollector.RecordResponseSize("tailor", len(result.Text()))
        }
    }
    
    return &output, err
}
```

## Metrics Exporters

### 1. Prometheus Exporter (Recommended for Production)
```go
func (p *Provider) createPrometheusExporter() (metric.Exporter, error) {
    return prometheus.New(
        prometheus.WithoutUnits(),
        prometheus.WithoutScopeInfo(),
    )
}
```

### 2. OTLP Exporter (For Observability Platforms)
```go
func (p *Provider) createOTLPExporter() (metric.Exporter, error) {
    return otlpmetricgrpc.New(ctx,
        otlpmetricgrpc.WithEndpoint(p.config.OTLPEndpoint),
        otlpmetricgrpc.WithHeaders(p.config.OTLPHeaders),
    )
}
```

### 3. Stdout Exporter (Development)
```go
func (p *Provider) createStdoutMetricsExporter() (metric.Exporter, error) {
    return stdoutmetric.New(
        stdoutmetric.WithWriter(os.Stderr),
        stdoutmetric.WithPrettyPrint(),
    )
}
```

## Dashboard Queries

### Grafana Dashboard Panels

#### Business Metrics
```promql
# Operations per minute
rate(resumatter_resumes_tailored_total[1m])
rate(resumatter_resumes_evaluated_total[1m])
rate(resumatter_jobs_analyzed_total[1m])

# ATS Score Distribution
histogram_quantile(0.95, resumatter_ats_score_bucket)
histogram_quantile(0.50, resumatter_ats_score_bucket)

# Success Rate
(
  rate(resumatter_requests_total[5m]) - 
  rate(resumatter_requests_failed_total[5m])
) / rate(resumatter_requests_total[5m]) * 100
```

#### Performance Metrics
```promql
# Operation Latency
histogram_quantile(0.95, resumatter_operation_duration_seconds_bucket)
histogram_quantile(0.50, resumatter_operation_duration_seconds_bucket)

# AI API Performance
histogram_quantile(0.95, resumatter_ai_request_duration_seconds_bucket)
rate(resumatter_ai_errors_total[5m])
```

#### System Health
```promql
# Concurrent Operations
resumatter_concurrent_operations

# Error Rate
rate(resumatter_requests_failed_total[5m]) / rate(resumatter_requests_total[5m])
```

## Alerting Rules

### Critical Alerts
```yaml
# High Error Rate
- alert: ResumatterHighErrorRate
  expr: rate(resumatter_requests_failed_total[5m]) / rate(resumatter_requests_total[5m]) > 0.1
  for: 2m
  labels:
    severity: critical
  annotations:
    summary: "Resumatter error rate is above 10%"

# High AI API Latency
- alert: ResumatterHighAILatency
  expr: histogram_quantile(0.95, resumatter_ai_request_duration_seconds_bucket) > 30
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "AI API latency is above 30 seconds"
```

### Warning Alerts
```yaml
# Low ATS Scores
- alert: ResumatterLowATSScores
  expr: histogram_quantile(0.50, resumatter_ats_score_bucket) < 50
  for: 10m
  labels:
    severity: warning
  annotations:
    summary: "Median ATS scores are below 50"
```

## Implementation Phases

### Phase 1: Core Metrics Infrastructure
1. Extend observability provider with metrics support
2. Add basic counters and histograms
3. Implement stdout exporter for development
4. Add metrics middleware to service layer

### Phase 2: Business Metrics
1. Add operation counters (tailor, evaluate, analyze)
2. Implement ATS and quality score histograms
3. Add content length and performance metrics
4. Create basic Grafana dashboard

### Phase 3: Advanced Metrics
1. Add AI service metrics (tokens, errors, latency)
2. Implement Prometheus exporter
3. Add integrity and quality metrics
4. Create comprehensive alerting rules

### Phase 4: Production Readiness
1. Add OTLP exporter for observability platforms
2. Implement metrics sampling and aggregation
3. Add custom metrics configuration
4. Performance optimization and testing

## Benefits

### Immediate Value
- **Performance Monitoring**: Track operation latencies and identify bottlenecks
- **Usage Analytics**: Understand application usage patterns
- **Quality Insights**: Monitor ATS scores and content quality trends
- **Error Tracking**: Real-time error rate monitoring

### Long-term Value
- **Capacity Planning**: Historical usage data for scaling decisions
- **Product Insights**: Understanding which features are most used
- **Quality Trends**: Track improvements in resume and job quality over time
- **Cost Optimization**: Monitor AI token usage and optimize costs

## Testing Strategy

### Unit Tests
- Test metrics collection without exporters
- Verify counter increments and histogram recordings
- Test error handling in metrics middleware

### Integration Tests
- Test with real exporters (stdout, prometheus)
- Verify metrics correlation with traces
- Test metrics in end-to-end scenarios

### Performance Tests
- Measure metrics collection overhead
- Test high-throughput scenarios
- Verify memory usage with metrics enabled
