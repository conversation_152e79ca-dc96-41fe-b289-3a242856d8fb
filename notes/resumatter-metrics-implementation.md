```go
// Example implementation of metrics for Resumatter
// This shows how metrics would be integrated into the existing observability framework

package observability

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/prometheus"
	"go.opentelemetry.io/otel/exporters/stdout/stdoutmetric"
	"go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"

	"resumatter/pkg/logger"
)

// MetricsExporter defines the type of metrics exporter to use
type MetricsExporter string

const (
	MetricsExporterPrometheus MetricsExporter = "prometheus"
	MetricsExporterStdout     MetricsExporter = "stdout"
	MetricsExporterOTLP       MetricsExporter = "otlp"
	MetricsExporterNone       MetricsExporter = "none"
)

// Enhanced Config with metrics support
type EnhancedConfig struct {
	*Config // Embed existing config
	
	// Metrics configuration
	MetricsEnabled  bool
	MetricsExporter MetricsExporter
	MetricsInterval time.Duration
}

// Enhanced Provider with metrics support
type EnhancedProvider struct {
	*Provider // Embed existing provider
	
	meterProvider metric.MeterProvider
	meter         metric.Meter
	
	// Pre-created instruments for performance
	instruments *MetricsInstruments
}

// MetricsInstruments holds all the metrics instruments
type MetricsInstruments struct {
	// Business metrics
	ResumesTailoredCounter    metric.Int64Counter
	ResumesEvaluatedCounter   metric.Int64Counter
	JobsAnalyzedCounter       metric.Int64Counter
	
	// Performance metrics
	OperationDurationHist     metric.Float64Histogram
	AIRequestDurationHist     metric.Float64Histogram
	
	// Quality metrics
	ATSScoreHist             metric.Float64Histogram
	JobQualityScoreHist      metric.Float64Histogram
	IntegrityIssuesCounter   metric.Int64Counter
	
	// System metrics
	RequestsTotal            metric.Int64Counter
	RequestsFailedTotal      metric.Int64Counter
	ConcurrentOperations     metric.Int64UpDownCounter
	
	// AI service metrics
	AITokensConsumedCounter  metric.Int64Counter
	AIRequestsTotal          metric.Int64Counter
	AIErrorsTotal            metric.Int64Counter
	AIResponseSizeHist       metric.Float64Histogram
}

// NewEnhancedProvider creates a new enhanced observability provider with metrics
func NewEnhancedProvider(config *EnhancedConfig) (*EnhancedProvider, error) {
	// Create base provider first
	baseProvider, err := NewProvider(config.Config)
	if err != nil {
		return nil, fmt.Errorf("failed to create base provider: %w", err)
	}
	
	provider := &EnhancedProvider{
		Provider: baseProvider,
	}
	
	// Initialize metrics if enabled
	if config.MetricsEnabled {
		if err := provider.initMetrics(config); err != nil {
			return nil, fmt.Errorf("failed to initialize metrics: %w", err)
		}
	}
	
	return provider, nil
}

// initMetrics initializes OpenTelemetry metrics
func (p *EnhancedProvider) initMetrics(config *EnhancedConfig) error {
	ctx := context.Background()
	p.logger.Info(ctx, "Initializing OpenTelemetry metrics",
		logger.String("exporter", string(config.MetricsExporter)))
	
	// Create resource (reuse from tracing)
	res, err := p.createResource()
	if err != nil {
		return fmt.Errorf("failed to create resource: %w", err)
	}
	
	// Create exporter based on configuration
	var exporter metric.Exporter
	switch config.MetricsExporter {
	case MetricsExporterPrometheus:
		exporter, err = p.createPrometheusExporter()
	case MetricsExporterStdout:
		exporter, err = p.createStdoutMetricsExporter()
	case MetricsExporterNone:
		// No exporter, metrics will be a no-op
		return nil
	default:
		return fmt.Errorf("unsupported metrics exporter: %s", config.MetricsExporter)
	}
	
	if err != nil {
		return fmt.Errorf("failed to create metrics exporter: %w", err)
	}
	
	// Create meter provider
	reader := metric.NewPeriodicReader(exporter,
		metric.WithInterval(config.MetricsInterval))
	
	p.meterProvider = metric.NewMeterProvider(
		metric.WithReader(reader),
		metric.WithResource(res),
	)
	
	// Set global meter provider
	otel.SetMeterProvider(p.meterProvider)
	
	// Create meter
	p.meter = p.meterProvider.Meter("resumatter")
	
	// Initialize instruments
	if err := p.initInstruments(); err != nil {
		return fmt.Errorf("failed to initialize instruments: %w", err)
	}
	
	p.logger.Info(ctx, "OpenTelemetry metrics initialized successfully")
	return nil
}

// initInstruments creates all the metrics instruments
func (p *EnhancedProvider) initInstruments() error {
	var err error
	p.instruments = &MetricsInstruments{}
	
	// Business metrics
	p.instruments.ResumesTailoredCounter, err = p.meter.Int64Counter(
		"resumatter_resumes_tailored_total",
		metric.WithDescription("Total number of resumes tailored"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return fmt.Errorf("failed to create resumes tailored counter: %w", err)
	}
	
	p.instruments.ResumesEvaluatedCounter, err = p.meter.Int64Counter(
		"resumatter_resumes_evaluated_total",
		metric.WithDescription("Total number of resumes evaluated"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return fmt.Errorf("failed to create resumes evaluated counter: %w", err)
	}
	
	p.instruments.JobsAnalyzedCounter, err = p.meter.Int64Counter(
		"resumatter_jobs_analyzed_total",
		metric.WithDescription("Total number of job descriptions analyzed"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return fmt.Errorf("failed to create jobs analyzed counter: %w", err)
	}
	
	// Performance metrics
	p.instruments.OperationDurationHist, err = p.meter.Float64Histogram(
		"resumatter_operation_duration_seconds",
		metric.WithDescription("Duration of operations in seconds"),
		metric.WithUnit("s"),
		metric.WithExplicitBucketBoundaries(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0),
	)
	if err != nil {
		return fmt.Errorf("failed to create operation duration histogram: %w", err)
	}
	
	p.instruments.AIRequestDurationHist, err = p.meter.Float64Histogram(
		"resumatter_ai_request_duration_seconds",
		metric.WithDescription("Duration of AI API requests in seconds"),
		metric.WithUnit("s"),
		metric.WithExplicitBucketBoundaries(0.5, 1.0, 2.0, 5.0, 10.0, 20.0, 30.0, 60.0),
	)
	if err != nil {
		return fmt.Errorf("failed to create AI request duration histogram: %w", err)
	}
	
	// Quality metrics
	p.instruments.ATSScoreHist, err = p.meter.Float64Histogram(
		"resumatter_ats_score",
		metric.WithDescription("Distribution of ATS scores"),
		metric.WithUnit("1"),
		metric.WithExplicitBucketBoundaries(0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100),
	)
	if err != nil {
		return fmt.Errorf("failed to create ATS score histogram: %w", err)
	}
	
	p.instruments.JobQualityScoreHist, err = p.meter.Float64Histogram(
		"resumatter_job_quality_score",
		metric.WithDescription("Distribution of job quality scores"),
		metric.WithUnit("1"),
		metric.WithExplicitBucketBoundaries(0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100),
	)
	if err != nil {
		return fmt.Errorf("failed to create job quality score histogram: %w", err)
	}
	
	p.instruments.IntegrityIssuesCounter, err = p.meter.Int64Counter(
		"resumatter_integrity_issues_total",
		metric.WithDescription("Total number of integrity issues found"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return fmt.Errorf("failed to create integrity issues counter: %w", err)
	}
	
	// System metrics
	p.instruments.RequestsTotal, err = p.meter.Int64Counter(
		"resumatter_requests_total",
		metric.WithDescription("Total number of requests"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return fmt.Errorf("failed to create requests total counter: %w", err)
	}
	
	p.instruments.RequestsFailedTotal, err = p.meter.Int64Counter(
		"resumatter_requests_failed_total",
		metric.WithDescription("Total number of failed requests"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return fmt.Errorf("failed to create requests failed counter: %w", err)
	}
	
	p.instruments.ConcurrentOperations, err = p.meter.Int64UpDownCounter(
		"resumatter_concurrent_operations",
		metric.WithDescription("Number of concurrent operations"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return fmt.Errorf("failed to create concurrent operations counter: %w", err)
	}
	
	// AI service metrics
	p.instruments.AITokensConsumedCounter, err = p.meter.Int64Counter(
		"resumatter_ai_tokens_consumed_total",
		metric.WithDescription("Total AI tokens consumed"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return fmt.Errorf("failed to create AI tokens consumed counter: %w", err)
	}
	
	p.instruments.AIRequestsTotal, err = p.meter.Int64Counter(
		"resumatter_ai_requests_total",
		metric.WithDescription("Total AI API requests"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return fmt.Errorf("failed to create AI requests total counter: %w", err)
	}
	
	p.instruments.AIErrorsTotal, err = p.meter.Int64Counter(
		"resumatter_ai_errors_total",
		metric.WithDescription("Total AI API errors"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return fmt.Errorf("failed to create AI errors total counter: %w", err)
	}
	
	p.instruments.AIResponseSizeHist, err = p.meter.Float64Histogram(
		"resumatter_ai_response_size_bytes",
		metric.WithDescription("Size of AI responses in bytes"),
		metric.WithUnit("By"),
		metric.WithExplicitBucketBoundaries(100, 500, 1000, 2000, 5000, 10000, 20000, 50000),
	)
	if err != nil {
		return fmt.Errorf("failed to create AI response size histogram: %w", err)
	}
	
	return nil
}

// createPrometheusExporter creates a Prometheus metrics exporter
func (p *EnhancedProvider) createPrometheusExporter() (metric.Exporter, error) {
	return prometheus.New(
		prometheus.WithoutUnits(),
		prometheus.WithoutScopeInfo(),
	)
}

// createStdoutMetricsExporter creates a stdout metrics exporter
func (p *EnhancedProvider) createStdoutMetricsExporter() (metric.Exporter, error) {
	return stdoutmetric.New(
		stdoutmetric.WithWriter(os.Stderr),
		stdoutmetric.WithPrettyPrint(),
	)
}

// MetricsCollector provides high-level metrics collection methods
type MetricsCollector struct {
	provider    *EnhancedProvider
	instruments *MetricsInstruments
}

// NewMetricsCollector creates a new metrics collector
func NewMetricsCollector(provider *EnhancedProvider) *MetricsCollector {
	return &MetricsCollector{
		provider:    provider,
		instruments: provider.instruments,
	}
}

// Business Metrics Methods

func (m *MetricsCollector) IncrementResumesTailored(ctx context.Context, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.ResumesTailoredCounter != nil {
		m.instruments.ResumesTailoredCounter.Add(ctx, 1, metric.WithAttributes(attributes...))
	}
}

func (m *MetricsCollector) IncrementResumesEvaluated(ctx context.Context, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.ResumesEvaluatedCounter != nil {
		m.instruments.ResumesEvaluatedCounter.Add(ctx, 1, metric.WithAttributes(attributes...))
	}
}

func (m *MetricsCollector) IncrementJobsAnalyzed(ctx context.Context, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.JobsAnalyzedCounter != nil {
		m.instruments.JobsAnalyzedCounter.Add(ctx, 1, metric.WithAttributes(attributes...))
	}
}

// Performance Metrics Methods

func (m *MetricsCollector) RecordOperationDuration(ctx context.Context, operation string, duration time.Duration, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.OperationDurationHist != nil {
		attrs := append(attributes, attribute.String("operation", operation))
		m.instruments.OperationDurationHist.Record(ctx, duration.Seconds(), metric.WithAttributes(attrs...))
	}
}

func (m *MetricsCollector) RecordAIRequestDuration(ctx context.Context, operation string, duration time.Duration, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.AIRequestDurationHist != nil {
		attrs := append(attributes, attribute.String("operation", operation))
		m.instruments.AIRequestDurationHist.Record(ctx, duration.Seconds(), metric.WithAttributes(attrs...))
	}
}

// Quality Metrics Methods

func (m *MetricsCollector) RecordATSScore(ctx context.Context, score int, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.ATSScoreHist != nil {
		m.instruments.ATSScoreHist.Record(ctx, float64(score), metric.WithAttributes(attributes...))
	}
}

func (m *MetricsCollector) RecordJobQualityScore(ctx context.Context, score int, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.JobQualityScoreHist != nil {
		m.instruments.JobQualityScoreHist.Record(ctx, float64(score), metric.WithAttributes(attributes...))
	}
}

func (m *MetricsCollector) IncrementIntegrityIssues(ctx context.Context, issueType string, count int, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.IntegrityIssuesCounter != nil {
		attrs := append(attributes, attribute.String("issue_type", issueType))
		m.instruments.IntegrityIssuesCounter.Add(ctx, int64(count), metric.WithAttributes(attrs...))
	}
}

// System Metrics Methods

func (m *MetricsCollector) IncrementRequests(ctx context.Context, operation string, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.RequestsTotal != nil {
		attrs := append(attributes, attribute.String("operation", operation))
		m.instruments.RequestsTotal.Add(ctx, 1, metric.WithAttributes(attrs...))
	}
}

func (m *MetricsCollector) IncrementFailedRequests(ctx context.Context, operation string, errorType string, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.RequestsFailedTotal != nil {
		attrs := append(attributes, 
			attribute.String("operation", operation),
			attribute.String("error_type", errorType))
		m.instruments.RequestsFailedTotal.Add(ctx, 1, metric.WithAttributes(attrs...))
	}
}

func (m *MetricsCollector) IncrementConcurrentOperations(ctx context.Context, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.ConcurrentOperations != nil {
		m.instruments.ConcurrentOperations.Add(ctx, 1, metric.WithAttributes(attributes...))
	}
}

func (m *MetricsCollector) DecrementConcurrentOperations(ctx context.Context, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.ConcurrentOperations != nil {
		m.instruments.ConcurrentOperations.Add(ctx, -1, metric.WithAttributes(attributes...))
	}
}

// AI Service Metrics Methods

func (m *MetricsCollector) IncrementAITokensConsumed(ctx context.Context, tokens int, model string, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.AITokensConsumedCounter != nil {
		attrs := append(attributes, attribute.String("model", model))
		m.instruments.AITokensConsumedCounter.Add(ctx, int64(tokens), metric.WithAttributes(attrs...))
	}
}

func (m *MetricsCollector) IncrementAIRequests(ctx context.Context, operation string, model string, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.AIRequestsTotal != nil {
		attrs := append(attributes, 
			attribute.String("operation", operation),
			attribute.String("model", model))
		m.instruments.AIRequestsTotal.Add(ctx, 1, metric.WithAttributes(attrs...))
	}
}

func (m *MetricsCollector) IncrementAIErrors(ctx context.Context, operation string, errorType string, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.AIErrorsTotal != nil {
		attrs := append(attributes, 
			attribute.String("operation", operation),
			attribute.String("error_type", errorType))
		m.instruments.AIErrorsTotal.Add(ctx, 1, metric.WithAttributes(attrs...))
	}
}

func (m *MetricsCollector) RecordAIResponseSize(ctx context.Context, operation string, sizeBytes int, attributes ...attribute.KeyValue) {
	if m.instruments != nil && m.instruments.AIResponseSizeHist != nil {
		attrs := append(attributes, attribute.String("operation", operation))
		m.instruments.AIResponseSizeHist.Record(ctx, float64(sizeBytes), metric.WithAttributes(attrs...))
	}
}

// Enhanced Shutdown method
func (p *EnhancedProvider) Shutdown(ctx context.Context) error {
	var errs []error
	
	// Shutdown metrics provider first
	if p.meterProvider != nil {
		p.logger.Info(ctx, "Shutting down OpenTelemetry meter provider")
		if err := p.meterProvider.Shutdown(ctx); err != nil {
			p.logger.ErrorWithErr(ctx, "Failed to shutdown meter provider", err)
			errs = append(errs, fmt.Errorf("failed to shutdown meter provider: %w", err))
		}
	}
	
	// Shutdown base provider (tracing)
	if err := p.Provider.Shutdown(ctx); err != nil {
		errs = append(errs, err)
	}
	
	// Return first error if any occurred
	if len(errs) > 0 {
		return errs[0]
	}
	return nil
}
```