//go:build fast

package fileio

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestReader_ReadFile(t *testing.T) {
	reader := NewReader()

	// Create a temporary file for testing
	tmpDir := t.TempDir()
	tmpFile := filepath.Join(tmpDir, "test.txt")
	testContent := "Hello, World!\nThis is a test file."

	err := os.WriteFile(tmpFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	tests := []struct {
		name     string
		filename string
		want     string
		wantErr  bool
	}{
		{
			name:     "read existing file",
			filename: tmpFile,
			want:     testContent,
			wantErr:  false,
		},
		{
			name:     "read non-existent file",
			filename: filepath.Join(tmpDir, "nonexistent.txt"),
			want:     "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := reader.ReadFile(tt.filename)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReadFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("ReadFile() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWriter_WriteFile(t *testing.T) {
	writer := NewWriter()

	tmpDir := t.TempDir()
	tmpFile := filepath.Join(tmpDir, "output.txt")
	testContent := "Test output content"

	err := writer.WriteFile(tmpFile, testContent)
	if err != nil {
		t.Fatalf("WriteFile() error = %v", err)
	}

	// Verify the file was written correctly
	content, err := os.ReadFile(tmpFile)
	if err != nil {
		t.Fatalf("Failed to read written file: %v", err)
	}

	if string(content) != testContent {
		t.Errorf("File content = %v, want %v", string(content), testContent)
	}
}

func TestWriter_WriteToStdout(t *testing.T) {
	writer := NewWriter()

	// This test just ensures the method doesn't panic
	// In a real scenario, you might want to capture stdout
	writer.WriteToStdout("test content")
}

func TestWriter_WriteFile_PermissionError(t *testing.T) {
	writer := NewWriter()

	// Try to write to a directory that doesn't exist or has no permissions
	invalidPath := "/root/nonexistent/test.txt"
	testContent := "test content"

	err := writer.WriteFile(invalidPath, testContent)
	if err == nil {
		t.Error("WriteFile() should error when writing to invalid path")
	}

	if !strings.Contains(err.Error(), "failed to write to file") {
		t.Errorf("WriteFile() error should contain 'failed to write to file', got: %v", err)
	}
}

func TestReader_ReadFile_DirectoryError(t *testing.T) {
	reader := NewReader()

	// Try to read a directory instead of a file
	tmpDir := t.TempDir()

	_, err := reader.ReadFile(tmpDir)
	if err == nil {
		t.Error("ReadFile() should error when trying to read a directory")
	}

	if !strings.Contains(err.Error(), "failed to read file") {
		t.Errorf("ReadFile() error should contain 'failed to read file', got: %v", err)
	}
}
