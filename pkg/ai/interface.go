package ai

import (
	"context"
	"resumatter/pkg/types"
)

type ClientInterface interface {
	TailorResume(ctx context.Context, input types.TailorResumeInput) (*types.TailorResumeOutput, error)
	EvaluateResume(ctx context.Context, input types.EvaluateResumeInput) (*types.EvaluateResumeOutput, error)
	AnalyzeJob(ctx context.Context, input types.AnalyzeJobInput) (*types.AnalyzeJobOutput, error)
	Close() error
}
