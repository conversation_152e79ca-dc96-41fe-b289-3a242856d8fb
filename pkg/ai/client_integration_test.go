//go:build integration

package ai

import (
	"context"
	"os"
	"strings"
	"testing"

	"resumatter/pkg/config"
	"resumatter/pkg/types"
)

// TestClient_TailorResume tests the TailorResume method with real API
func TestClient_TailorResume(t *testing.T) {
	// Skip if no API key available
	apiKey := getAPIKey()
	if apiKey == "" {
		t.<PERSON><PERSON>("Skipping TailorResume test: no API key available")
	}

	config := &config.Config{
		APIKey:      apiKey,
		Model:       "gemini-2.0-flash-lite",
		Temperature: 0.7,
	}

	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer client.Close()

	input := types.TailorResumeInput{
		BaseResume:     "Software Engineer with 3 years experience in Go and Python.",
		JobDescription: "Looking for a Go developer with backend experience.",
	}

	ctx := context.Background()
	output, err := client.TailorResume(ctx, input)
	if err != nil {
		t.Fatalf("TailorResume() error = %v", err)
	}

	// Validate output structure
	if output == nil {
		t.Fatal("TailorResume() returned nil output")
	}

	if output.TailoredResume == "" {
		t.Error("TailorResume() returned empty tailored resume")
	}

	if output.ATSAnalysis.Score < 0 || output.ATSAnalysis.Score > 100 {
		t.Errorf("TailorResume() ATS score = %d, want 0-100", output.ATSAnalysis.Score)
	}

	if output.ATSAnalysis.Strengths == "" {
		t.Error("TailorResume() returned empty ATS strengths")
	}

	if output.JobPostingAnalysis.Clarity == "" {
		t.Error("TailorResume() returned empty job posting clarity analysis")
	}
}

// TestClient_EvaluateResume tests the EvaluateResume method with real API
func TestClient_EvaluateResume(t *testing.T) {
	// Skip if no API key available
	apiKey := getAPIKey()
	if apiKey == "" {
		t.Skip("Skipping EvaluateResume test: no API key available")
	}

	config := &config.Config{
		APIKey:      apiKey,
		Model:       "gemini-2.0-flash-lite",
		Temperature: 0.7,
	}

	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer client.Close()

	input := types.EvaluateResumeInput{
		BaseResume:     "Software Engineer with 3 years experience in Go and Python.",
		TailoredResume: "Senior Software Engineer with 5 years experience in Go, Python, and machine learning.",
	}

	ctx := context.Background()
	output, err := client.EvaluateResume(ctx, input)
	if err != nil {
		t.Fatalf("EvaluateResume() error = %v", err)
	}

	// Validate output structure
	if output == nil {
		t.Fatal("EvaluateResume() returned nil output")
	}

	if output.Summary == "" {
		t.Error("EvaluateResume() returned empty summary")
	}

	if output.Findings == nil {
		t.Error("EvaluateResume() returned nil findings")
	}

	// Findings should contain at least one issue since we added fabricated content
	if len(output.Findings) == 0 {
		t.Error("EvaluateResume() should have detected fabrications but found none")
	}

	// Validate finding structure if any exist
	for i, finding := range output.Findings {
		if finding.Type == "" {
			t.Errorf("EvaluateResume() finding %d has empty type", i)
		}
		if finding.Description == "" {
			t.Errorf("EvaluateResume() finding %d has empty description", i)
		}
		if finding.Evidence == "" {
			t.Errorf("EvaluateResume() finding %d has empty evidence", i)
		}
	}
}

// TestClient_AnalyzeJob tests the AnalyzeJob method with real API
func TestClient_AnalyzeJob(t *testing.T) {
	// Skip if no API key available
	apiKey := getAPIKey()
	if apiKey == "" {
		t.Skip("Skipping AnalyzeJob test: no API key available")
	}

	config := &config.Config{
		APIKey:      apiKey,
		Model:       "gemini-2.0-flash-lite",
		Temperature: 0.7,
	}

	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer client.Close()

	input := types.AnalyzeJobInput{
		JobDescription: "We need a rockstar ninja developer who can work 80 hours a week for minimum wage.",
	}

	ctx := context.Background()
	output, err := client.AnalyzeJob(ctx, input)
	if err != nil {
		t.Fatalf("AnalyzeJob() error = %v", err)
	}

	// Validate output structure
	if output == nil {
		t.Fatal("AnalyzeJob() returned nil output")
	}

	// Validate all components.
	validateJobQualityScore(t, output.JobQualityScore)
	validateClarityAnalysis(t, output.Clarity)
	validateInclusivityAnalysis(t, output.Inclusivity)
	validateCandidateAttractionScore(t, output.CandidateAttraction.Score)
	validateMarketCompetitiveness(t, output.MarketCompetitiveness)
	validateRecommendations(t, output.Recommendations)
	validateFlaggedTerms(t, output.Inclusivity.FlaggedTerms)
}

// validateJobQualityScore validates the job quality score is within valid range
func validateJobQualityScore(t *testing.T, score int) {
	if score < 0 || score > 100 {
		t.Errorf("AnalyzeJob() JobQualityScore = %d, want 0-100", score)
	}
}

// validateClarityAnalysis validates the clarity analysis structure
func validateClarityAnalysis(t *testing.T, clarity types.JobQualityScore) {
	if clarity.Score < 0 || clarity.Score > 100 {
		t.Errorf("AnalyzeJob() Clarity.Score = %d, want 0-100", clarity.Score)
	}
	if clarity.Analysis == "" {
		t.Error("AnalyzeJob() returned empty clarity analysis")
	}
}

// validateInclusivityAnalysis validates the inclusivity analysis structure
func validateInclusivityAnalysis(t *testing.T, inclusivity types.InclusivityAnalysis) {
	if inclusivity.Score < 0 || inclusivity.Score > 100 {
		t.Errorf("AnalyzeJob() Inclusivity.Score = %d, want 0-100", inclusivity.Score)
	}
	if inclusivity.Analysis == "" {
		t.Error("AnalyzeJob() returned empty inclusivity analysis")
	}
}

// validateCandidateAttractionScore validates the candidate attraction score
func validateCandidateAttractionScore(t *testing.T, score int) {
	if score < 0 || score > 100 {
		t.Errorf("AnalyzeJob() CandidateAttraction.Score = %d, want 0-100", score)
	}
}

// validateMarketCompetitiveness validates the market competitiveness analysis
func validateMarketCompetitiveness(t *testing.T, market types.MarketCompetitiveness) {
	if market.SalaryTransparency == "" {
		t.Error("AnalyzeJob() returned empty salary transparency analysis")
	}
}

// validateRecommendations validates that recommendations are not nil
func validateRecommendations(t *testing.T, recommendations []string) {
	if recommendations == nil {
		t.Error("AnalyzeJob() returned nil recommendations")
	}
}

// validateFlaggedTerms validates that problematic terms were flagged
func validateFlaggedTerms(t *testing.T, flaggedTerms []string) {
	if len(flaggedTerms) == 0 {
		t.Error("AnalyzeJob() should have flagged problematic terms but found none")
	}
}

// getAPIKey reads the API key from file or environment
func getAPIKey() string {
	// Try reading from file first (relative to project root)
	if data, err := os.ReadFile("../../.key/gemini"); err == nil {
		return strings.TrimSpace(string(data))
	}

	// Try from current directory
	if data, err := os.ReadFile(".key/gemini"); err == nil {
		return strings.TrimSpace(string(data))
	}

	// Fall back to environment variables
	if key := os.Getenv("GEMINI_API_KEY"); key != "" {
		return key
	}

	if key := os.Getenv("RESUMATTER_AI_APIKEY"); key != "" {
		return key
	}

	return ""
}
