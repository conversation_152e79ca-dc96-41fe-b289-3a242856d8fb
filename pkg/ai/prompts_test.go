//go:build fast

package ai

import (
	"strings"
	"testing"
)

func TestSystemPrompts(t *testing.T) {
	tests := []struct {
		name     string
		key      string
		expected []string // keywords that should be present
	}{
		{
			name: "tailor prompt exists",
			key:  "tailor",
			expected: []string{
				"resume writer",
				"HR analyst",
				"NEVER invent",
				"ATS",
				"integrity",
			},
		},
		{
			name: "evaluate prompt exists",
			key:  "evaluate",
			expected: []string{
				"resume reviewer",
				"integrity analyst",
				"Overclaims",
				"Inventions",
				"Incorrect Linking",
			},
		},
		{
			name: "analyze prompt exists",
			key:  "analyze",
			expected: []string{
				"HR consultant",
				"recruitment specialist",
				"inclusive hiring",
				"bias detection",
				"job descriptions",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prompt, exists := SystemPrompts[tt.key]
			if !exists {
				t.Errorf("SystemPrompts[%s] does not exist", tt.key)
				return
			}

			if prompt == "" {
				t.<PERSON><PERSON><PERSON>("SystemPrompts[%s] is empty", tt.key)
				return
			}

			for _, keyword := range tt.expected {
				if !strings.Contains(strings.ToLower(prompt), strings.ToLower(keyword)) {
					t.Errorf("SystemPrompts[%s] missing expected keyword: %s", tt.key, keyword)
				}
			}
		})
	}
}

func TestUserPrompts(t *testing.T) {
	tests := []struct {
		name     string
		key      string
		expected []string // keywords that should be present
	}{
		{
			name: "tailor prompt exists",
			key:  "tailor",
			expected: []string{
				"Tailor Resume",
				"ATS Analysis",
				"Job Posting Analysis",
				"Base Resume:",
				"Job Description:",
			},
		},
		{
			name: "evaluate prompt exists",
			key:  "evaluate",
			expected: []string{
				"Overclaim",
				"Invention",
				"Incorrect Linking",
				"Base Resume:",
				"Tailored Resume:",
			},
		},
		{
			name: "analyze prompt exists",
			key:  "analyze",
			expected: []string{
				"job description",
				"Clarity Assessment",
				"Inclusivity Analysis",
				"Candidate Attraction",
				"Market Competitiveness",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prompt, exists := UserPrompts[tt.key]
			if !exists {
				t.Errorf("UserPrompts[%s] does not exist", tt.key)
				return
			}

			if prompt == "" {
				t.Errorf("UserPrompts[%s] is empty", tt.key)
				return
			}

			for _, keyword := range tt.expected {
				if !strings.Contains(prompt, keyword) {
					t.Errorf("UserPrompts[%s] missing expected keyword: %s", tt.key, keyword)
				}
			}
		})
	}
}

func TestUserPromptsFormatting(t *testing.T) {
	tests := []struct {
		name                 string
		key                  string
		expectedPlaceholders int
	}{
		{
			name:                 "tailor prompt has 2 placeholders",
			key:                  "tailor",
			expectedPlaceholders: 2, // %s for resume and job description
		},
		{
			name:                 "evaluate prompt has 2 placeholders",
			key:                  "evaluate",
			expectedPlaceholders: 2, // %s for base and tailored resume
		},
		{
			name:                 "analyze prompt has 1 placeholder",
			key:                  "analyze",
			expectedPlaceholders: 1, // %s for job description
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prompt, exists := UserPrompts[tt.key]
			if !exists {
				t.Errorf("UserPrompts[%s] does not exist", tt.key)
				return
			}

			placeholderCount := strings.Count(prompt, "%s")
			if placeholderCount != tt.expectedPlaceholders {
				t.Errorf("UserPrompts[%s] has %d placeholders, want %d", tt.key, placeholderCount, tt.expectedPlaceholders)
			}
		})
	}
}
