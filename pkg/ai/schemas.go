package ai

import "google.golang.org/genai"

// buildTailorSchema builds the JSON schema for tailor resume response
func (c *Client) buildTailorSchema() *genai.Schema {
	return &genai.Schema{
		Type: genai.TypeObject,
		Properties: map[string]*genai.Schema{
			"tailoredResume": {Type: genai.TypeString},
			"atsAnalysis": {
				Type: genai.TypeObject,
				Properties: map[string]*genai.Schema{
					"score":      {Type: genai.TypeInteger},
					"strengths":  {Type: genai.TypeString},
					"weaknesses": {Type: genai.TypeString},
				},
				Required: []string{"score", "strengths", "weaknesses"},
			},
			"jobPostingAnalysis": {
				Type: genai.TypeObject,
				Properties: map[string]*genai.Schema{
					"clarity":     {Type: genai.TypeString},
					"inclusivity": {Type: genai.TypeString},
					"quality":     {Type: genai.TypeString},
				},
				Required: []string{"clarity", "inclusivity", "quality"},
			},
		},
		Required: []string{"tailoredResume", "atsAnalysis", "jobPostingAnalysis"},
	}
}

// buildEvaluateSchema builds the JSON schema for evaluate resume response
func (c *Client) buildEvaluateSchema() *genai.Schema {
	return &genai.Schema{
		Type: genai.TypeObject,
		Properties: map[string]*genai.Schema{
			"summary": {Type: genai.TypeString},
			"findings": {
				Type: genai.TypeArray,
				Items: &genai.Schema{
					Type: genai.TypeObject,
					Properties: map[string]*genai.Schema{
						"type":        {Type: genai.TypeString},
						"description": {Type: genai.TypeString},
						"evidence":    {Type: genai.TypeString},
					},
					Required: []string{"type", "description", "evidence"},
				},
			},
		},
		Required: []string{"summary", "findings"},
	}
}

// buildAnalyzeSchema builds the JSON schema for analyze job response
func (c *Client) buildAnalyzeSchema() *genai.Schema {
	return &genai.Schema{
		Type: genai.TypeObject,
		Properties: map[string]*genai.Schema{
			"jobQualityScore": {Type: genai.TypeInteger},
			"clarity": {
				Type: genai.TypeObject,
				Properties: map[string]*genai.Schema{
					"score":    {Type: genai.TypeInteger},
					"analysis": {Type: genai.TypeString},
					"improvements": {
						Type:  genai.TypeArray,
						Items: &genai.Schema{Type: genai.TypeString},
					},
				},
				Required: []string{"score", "analysis", "improvements"},
			},
			"inclusivity": {
				Type: genai.TypeObject,
				Properties: map[string]*genai.Schema{
					"score":    {Type: genai.TypeInteger},
					"analysis": {Type: genai.TypeString},
					"flaggedTerms": {
						Type:  genai.TypeArray,
						Items: &genai.Schema{Type: genai.TypeString},
					},
					"suggestions": {
						Type:  genai.TypeArray,
						Items: &genai.Schema{Type: genai.TypeString},
					},
				},
				Required: []string{"score", "analysis", "flaggedTerms", "suggestions"},
			},
			"candidateAttraction": {
				Type: genai.TypeObject,
				Properties: map[string]*genai.Schema{
					"score": {Type: genai.TypeInteger},
					"strengths": {
						Type:  genai.TypeArray,
						Items: &genai.Schema{Type: genai.TypeString},
					},
					"weaknesses": {
						Type:  genai.TypeArray,
						Items: &genai.Schema{Type: genai.TypeString},
					},
				},
				Required: []string{"score", "strengths", "weaknesses"},
			},
			"marketCompetitiveness": {
				Type: genai.TypeObject,
				Properties: map[string]*genai.Schema{
					"salaryTransparency":  {Type: genai.TypeString},
					"requirementsRealism": {Type: genai.TypeString},
					"industryAlignment":   {Type: genai.TypeString},
				},
				Required: []string{"salaryTransparency", "requirementsRealism", "industryAlignment"},
			},
			"recommendations": {
				Type:  genai.TypeArray,
				Items: &genai.Schema{Type: genai.TypeString},
			},
		},
		Required: []string{"jobQualityScore", "clarity", "inclusivity", "candidateAttraction", "marketCompetitiveness", "recommendations"},
	}
}
