//go:build fast

package ai

import (
	"os"
	"strings"
	"testing"

	"resumatter/pkg/config"

	"google.golang.org/genai"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name    string
		config  *config.Config
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid config",
			config: &config.Config{
				APIKey:      "test-api-key",
				Model:       "gemini-2.0-flash",
				Temperature: 0.7,
			},
			wantErr: false,
		},
		{
			name: "missing API key",
			config: &config.Config{
				APIKey:      "",
				Model:       "gemini-2.0-flash",
				Temperature: 0.7,
			},
			wantErr: true,
			errMsg:  "API key is required",
		},
		{
			name: "vertex AI missing project",
			config: &config.Config{
				UseVertexAI:       true,
				GoogleCloudRegion: "us-west1",
				Model:             "gemini-2.0-flash",
				Temperature:       0.7,
			},
			wantErr: true,
			errMsg:  "project/location or API key must be set when using Vertex AI backend",
		},
		{
			name: "vertex AI missing region",
			config: &config.Config{
				UseVertexAI:        true,
				GoogleCloudProject: "test-project",
				Model:              "gemini-2.0-flash",
				Temperature:        0.7,
			},
			wantErr: true,
			errMsg:  "project/location or API key must be set when using Vertex AI backend",
		},
		{
			name: "vertex AI with project and region but no credentials",
			config: &config.Config{
				UseVertexAI:        true,
				GoogleCloudProject: "dummy",
				GoogleCloudRegion:  "us-west1",
				Model:              "gemini-2.0-flash",
				Temperature:        0.7,
			},
			wantErr: true,
			errMsg:  "failed to find default credentials",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// For Vertex AI tests, temporarily clear API key to test validation
			if tt.config.UseVertexAI && (tt.config.GoogleCloudProject == "" || tt.config.GoogleCloudRegion == "") {
				oldKey := os.Getenv("GEMINI_API_KEY")
				os.Setenv("GEMINI_API_KEY", "")
				defer os.Setenv("GEMINI_API_KEY", oldKey)
			}

			client, err := New(tt.config)

			if tt.wantErr {
				if err == nil {
					t.Errorf("New() expected error but got none")
					return
				}
				if tt.errMsg != "" && !contains(err.Error(), tt.errMsg) {
					t.Errorf("New() error = %v, want error containing %v", err, tt.errMsg)
				}
				return
			}

			if err != nil {
				t.Errorf("New() unexpected error = %v", err)
				return
			}

			if client == nil {
				t.Errorf("New() returned nil client")
				return
			}

			if client.config != tt.config {
				t.Errorf("New() client.config = %v, want %v", client.config, tt.config)
			}
		})
	}
}

func TestClient_Close(t *testing.T) {
	config := &config.Config{
		APIKey:      "test-api-key",
		Model:       "gemini-2.0-flash",
		Temperature: 0.7,
	}

	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	err = client.Close()
	if err != nil {
		t.Errorf("Close() error = %v, want nil", err)
	}
}

func TestClient_CloseNilClient(t *testing.T) {
	client := &Client{}

	err := client.Close()
	if err != nil {
		t.Errorf("Close() on nil client error = %v, want nil", err)
	}
}

func TestClient_buildTailorSchema(t *testing.T) {
	config := &config.Config{
		APIKey:      "test-api-key",
		Model:       "gemini-2.0-flash",
		Temperature: 0.7,
	}

	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	schema := client.buildTailorSchema()

	if schema == nil {
		t.Fatal("buildTailorSchema() returned nil")
	}

	if schema.Type != genai.TypeObject {
		t.Errorf("buildTailorSchema() Type = %v, want %v", schema.Type, genai.TypeObject)
	}

	// Check required properties exist
	requiredProps := []string{"tailoredResume", "atsAnalysis", "jobPostingAnalysis"}
	for _, prop := range requiredProps {
		if _, exists := schema.Properties[prop]; !exists {
			t.Errorf("buildTailorSchema() missing required property: %s", prop)
		}
	}

	// Check required fields
	if len(schema.Required) != 3 {
		t.Errorf("buildTailorSchema() Required length = %d, want 3", len(schema.Required))
	}
}

func TestClient_buildEvaluateSchema(t *testing.T) {
	config := &config.Config{
		APIKey:      "test-api-key",
		Model:       "gemini-2.0-flash",
		Temperature: 0.7,
	}

	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	schema := client.buildEvaluateSchema()

	if schema == nil {
		t.Fatal("buildEvaluateSchema() returned nil")
	}

	if schema.Type != genai.TypeObject {
		t.Errorf("buildEvaluateSchema() Type = %v, want %v", schema.Type, genai.TypeObject)
	}

	// Check required properties exist
	requiredProps := []string{"summary", "findings"}
	for _, prop := range requiredProps {
		if _, exists := schema.Properties[prop]; !exists {
			t.Errorf("buildEvaluateSchema() missing required property: %s", prop)
		}
	}

	// Check findings is an array
	findingsSchema := schema.Properties["findings"]
	if findingsSchema.Type != genai.TypeArray {
		t.Errorf("buildEvaluateSchema() findings Type = %v, want %v", findingsSchema.Type, genai.TypeArray)
	}
}

func TestClient_buildAnalyzeSchema(t *testing.T) {
	config := &config.Config{
		APIKey:      "test-api-key",
		Model:       "gemini-2.0-flash",
		Temperature: 0.7,
	}

	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	schema := client.buildAnalyzeSchema()

	if schema == nil {
		t.Fatal("buildAnalyzeSchema() returned nil")
	}

	if schema.Type != genai.TypeObject {
		t.Errorf("buildAnalyzeSchema() Type = %v, want %v", schema.Type, genai.TypeObject)
	}

	// Check required properties exist
	requiredProps := []string{"jobQualityScore", "clarity", "inclusivity", "candidateAttraction", "marketCompetitiveness", "recommendations"}
	for _, prop := range requiredProps {
		if _, exists := schema.Properties[prop]; !exists {
			t.Errorf("buildAnalyzeSchema() missing required property: %s", prop)
		}
	}

	// Check jobQualityScore is integer
	scoreSchema := schema.Properties["jobQualityScore"]
	if scoreSchema.Type != genai.TypeInteger {
		t.Errorf("buildAnalyzeSchema() jobQualityScore Type = %v, want %v", scoreSchema.Type, genai.TypeInteger)
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return strings.Contains(s, substr)
}

// Note: API integration tests have been moved to client_integration_test.go
// These unit tests focus on testing the client without making actual API calls
