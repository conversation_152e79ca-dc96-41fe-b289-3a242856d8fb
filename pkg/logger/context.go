package logger

import (
	"context"
)

// contextKey is a private type for context keys to avoid collisions
type contextKey string

const (
	loggerContextKey contextKey = "logger"
)

// WithLogger adds a logger to the context
func WithLogger(ctx context.Context, logger Logger) context.Context {
	return context.WithValue(ctx, loggerContextKey, logger)
}

// FromContext extracts a logger from the context
// If no logger is found, returns a default logger
func FromContext(ctx context.Context) Logger {
	if logger, ok := ctx.Value(loggerContextKey).(Logger); ok {
		return logger
	}
	return NewDefault()
}

// FromContextOrNil extracts a logger from the context
// Returns nil if no logger is found
func FromContextOrNil(ctx context.Context) Logger {
	if logger, ok := ctx.Value(loggerContextKey).(Logger); ok {
		return logger
	}
	return nil
}

// Convenience functions that use the logger from context

// Debug logs a debug message using the logger from context
func Debug(ctx context.Context, msg string, fields ...Field) {
	FromContext(ctx).Debug(ctx, msg, fields...)
}

// Info logs an info message using the logger from context
func Info(ctx context.Context, msg string, fields ...Field) {
	FromContext(ctx).Info(ctx, msg, fields...)
}

// Warn logs a warning message using the logger from context
func Warn(ctx context.Context, msg string, fields ...Field) {
	FromContext(ctx).Warn(ctx, msg, fields...)
}

// Error logs an error message using the logger from context
func Error(ctx context.Context, msg string, fields ...Field) {
	FromContext(ctx).Error(ctx, msg, fields...)
}

// ErrorWithErr logs an error message with an error field using the logger from context
func ErrorWithErr(ctx context.Context, msg string, err error, fields ...Field) {
	FromContext(ctx).ErrorWithErr(ctx, msg, err, fields...)
}
