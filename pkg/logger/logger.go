package logger

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"time"

	"go.opentelemetry.io/otel/trace"
)

// Config holds logger configuration
type Config struct {
	Level      Level
	Format     Format
	Output     io.Writer
	TimeFormat string
}

// Format represents the output format
type Format string

const (
	FormatJSON Format = "json"
	FormatText Format = "text"
)

// DefaultConfig returns a default logger configuration
func DefaultConfig() *Config {
	return &Config{
		Level:      LevelInfo,
		Format:     FormatText,
		Output:     os.Stderr,
		TimeFormat: time.RFC3339,
	}
}

// logger implements the Logger interface
type logger struct {
	config *Config
	fields []Field
	name   string
}

// New creates a new logger with the given configuration
func New(config *Config) Logger {
	if config == nil {
		config = DefaultConfig()
	}
	return &logger{
		config: config,
		fields: make([]Field, 0),
	}
}

// NewDefault creates a logger with default configuration
func NewDefault() Logger {
	return New(DefaultConfig())
}

// Debug logs a debug message
func (l *logger) Debug(ctx context.Context, msg string, fields ...Field) {
	l.log(ctx, LevelDebug, msg, fields...)
}

// Info logs an info message
func (l *logger) Info(ctx context.Context, msg string, fields ...Field) {
	l.log(ctx, LevelInfo, msg, fields...)
}

// Warn logs a warning message
func (l *logger) Warn(ctx context.Context, msg string, fields ...Field) {
	l.log(ctx, LevelWarn, msg, fields...)
}

// Error logs an error message
func (l *logger) Error(ctx context.Context, msg string, fields ...Field) {
	l.log(ctx, LevelError, msg, fields...)
}

// ErrorWithErr logs an error message with an error field
func (l *logger) ErrorWithErr(ctx context.Context, msg string, err error, fields ...Field) {
	allFields := append(fields, Err(err))
	l.log(ctx, LevelError, msg, allFields...)
}

// SetLevel sets the logging level
func (l *logger) SetLevel(level Level) {
	l.config.Level = level
}

// GetLevel returns the current logging level
func (l *logger) GetLevel() Level {
	return l.config.Level
}

// With returns a new logger with additional fields
func (l *logger) With(fields ...Field) Logger {
	newFields := make([]Field, len(l.fields)+len(fields))
	copy(newFields, l.fields)
	copy(newFields[len(l.fields):], fields)

	return &logger{
		config: l.config,
		fields: newFields,
		name:   l.name,
	}
}

// Named returns a new logger with a name
func (l *logger) Named(name string) Logger {
	newName := name
	if l.name != "" {
		newName = l.name + "." + name
	}

	return &logger{
		config: l.config,
		fields: l.fields,
		name:   newName,
	}
}

// log is the core logging method
func (l *logger) log(ctx context.Context, level Level, msg string, fields ...Field) {
	// Check if we should log at this level
	if level < l.config.Level {
		return
	}

	// Create log entry
	entry := l.createLogEntry(ctx, level, msg, fields...)

	// Format and write
	var output string
	switch l.config.Format {
	case FormatJSON:
		output = l.formatJSON(entry)
	default:
		output = l.formatText(entry)
	}

	if _, err := fmt.Fprintln(l.config.Output, output); err != nil {
		fmt.Fprintln(os.Stderr, "error writing log output:", err)
	}
}

// LogEntry represents a single log entry
type LogEntry struct {
	Timestamp string         `json:"timestamp"`
	Level     string         `json:"level"`
	Message   string         `json:"message"`
	Logger    string         `json:"logger,omitempty"`
	TraceID   string         `json:"trace_id,omitempty"`
	SpanID    string         `json:"span_id,omitempty"`
	Fields    map[string]any `json:"fields,omitempty"`
}

// createLogEntry creates a structured log entry
func (l *logger) createLogEntry(ctx context.Context, level Level, msg string, fields ...Field) *LogEntry {
	entry := &LogEntry{
		Timestamp: time.Now().Format(l.config.TimeFormat),
		Level:     level.String(),
		Message:   msg,
		Logger:    l.name,
		Fields:    make(map[string]any),
	}

	// Extract OpenTelemetry trace information
	if span := trace.SpanFromContext(ctx); span.SpanContext().IsValid() {
		spanCtx := span.SpanContext()
		entry.TraceID = spanCtx.TraceID().String()
		entry.SpanID = spanCtx.SpanID().String()
	}

	// Add logger's persistent fields
	for _, field := range l.fields {
		entry.Fields[field.Key] = field.Value
	}

	// Add call-specific fields
	for _, field := range fields {
		entry.Fields[field.Key] = field.Value
	}

	// Remove fields map if empty
	if len(entry.Fields) == 0 {
		entry.Fields = nil
	}

	return entry
}

// formatJSON formats the log entry as JSON
func (l *logger) formatJSON(entry *LogEntry) string {
	data, err := json.Marshal(entry)
	if err != nil {
		// Fallback to simple format if JSON marshaling fails
		return fmt.Sprintf(`{"timestamp":"%s","level":"%s","message":"JSON marshal error: %v","original_message":"%s"}`,
			entry.Timestamp, entry.Level, err, entry.Message)
	}
	return string(data)
}

// formatText formats the log entry as human-readable text
func (l *logger) formatText(entry *LogEntry) string {
	base := fmt.Sprintf("%s [%s] %s", entry.Timestamp, entry.Level, entry.Message)

	// Add logger name if present
	if entry.Logger != "" {
		base = fmt.Sprintf("%s [%s] [%s] %s", entry.Timestamp, entry.Level, entry.Logger, entry.Message)
	}

	// Add trace information if present
	if entry.TraceID != "" {
		base += fmt.Sprintf(" trace_id=%s", entry.TraceID)
	}
	if entry.SpanID != "" {
		base += fmt.Sprintf(" span_id=%s", entry.SpanID)
	}

	// Add fields if present
	if len(entry.Fields) > 0 {
		for key, value := range entry.Fields {
			base += fmt.Sprintf(" %s=%v", key, value)
		}
	}

	return base
}
