package logger

import (
	"context"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

// OperationLogger provides structured logging for operations with automatic tracing
type OperationLogger struct {
	logger Logger
	tracer trace.Tracer
}

// NewOperationLogger creates a new operation logger
func NewOperationLogger(logger Logger, tracerName string) *OperationLogger {
	return &OperationLogger{
		logger: logger,
		tracer: otel.Tracer(tracerName),
	}
}

// OperationConfig configures an operation
type OperationConfig struct {
	Name        string
	Component   string
	Fields      []Field
	Attributes  []attribute.KeyValue
	SkipLogging bool
}

// WithOperation wraps an operation with logging and tracing
func (ol *OperationLogger) WithOperation(ctx context.Context, config OperationConfig, fn func(ctx context.Context) error) error {
	// Start span
	ctx, span := ol.tracer.Start(ctx, config.Name)
	defer span.End()

	// Add attributes to span
	if len(config.Attributes) > 0 {
		span.SetAttributes(config.Attributes...)
	}

	// Create operation logger
	opLogger := ol.logger
	if config.Component != "" {
		opLogger = opLogger.Named(config.Component)
	}
	if len(config.Fields) > 0 {
		opLogger = opLogger.With(config.Fields...)
	}

	// Add logger to context
	ctx = WithLogger(ctx, opLogger)

	// Log operation start
	if !config.SkipLogging {
		opLogger.Debug(ctx, "Operation started", String("operation", config.Name))
	}

	start := time.Now()
	err := fn(ctx)
	duration := time.Since(start)

	// Log operation completion
	if !config.SkipLogging {
		if err != nil {
			opLogger.ErrorWithErr(ctx, "Operation failed", err,
				String("operation", config.Name),
				Duration("duration", duration),
			)
			span.RecordError(err)
		} else {
			opLogger.Debug(ctx, "Operation completed",
				String("operation", config.Name),
				Duration("duration", duration),
			)
		}
	}

	// Add duration to span
	span.SetAttributes(attribute.String("duration", duration.String()))

	return err
}

// LoggedOperation is a convenience wrapper for simple operations
func (ol *OperationLogger) LoggedOperation(ctx context.Context, name string, fn func(ctx context.Context) error) error {
	return ol.WithOperation(ctx, OperationConfig{Name: name}, fn)
}

// ComponentOperation creates an operation logger for a specific component
func (ol *OperationLogger) ComponentOperation(ctx context.Context, component, operation string, fn func(ctx context.Context) error) error {
	return ol.WithOperation(ctx, OperationConfig{
		Name:      operation,
		Component: component,
	}, fn)
}
