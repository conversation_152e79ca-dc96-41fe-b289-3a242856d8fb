package logger

import (
	"bytes"
	"context"
	"errors"
	"strings"
	"testing"

	"go.opentelemetry.io/otel"
	otelstdout "go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	otelresource "go.opentelemetry.io/otel/sdk/resource"
	oteltrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.34.0"
)

func setupTracerProvider(t *testing.T) func() {
	exp, err := otelstdout.New(otelstdout.WithPrettyPrint())
	if err != nil {
		t.Fatalf("failed to initialize stdouttrace exporter: %v", err)
	}
	res, err := otelresource.Merge(
		otelresource.Default(),
		otelresource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName("operation-logger-test"),
		),
	)
	if err != nil {
		t.Fatalf("failed to create resource: %v", err)
	}
	tp := oteltrace.NewTracerProvider(
		oteltrace.WithBatcher(exp),
		oteltrace.WithResource(res),
	)
	otel.SetTracerProvider(tp)
	return func() { _ = tp.Shutdown(context.Background()) }
}

func TestOperationLogger_WithOperation_Success(t *testing.T) {
	teardown := setupTracerProvider(t)
	defer teardown()

	var buf bytes.Buffer
	config := &Config{
		Level:  LevelDebug,
		Format: FormatJSON,
		Output: &buf,
	}
	log := New(config)
	opLogger := NewOperationLogger(log, "test-tracer")
	ctx := context.Background()

	err := opLogger.WithOperation(ctx, OperationConfig{
		Name:      "test-operation",
		Component: "test-component",
		Fields:    []Field{String("foo", "bar")},
	}, func(ctx context.Context) error {
		Info(ctx, "inside operation", String("inside", "yes"))
		return nil
	})
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}

	output := buf.String()
	if !strings.Contains(output, "Operation started") {
		t.Errorf("expected 'Operation started' log, got: %s", output)
	}
	if !strings.Contains(output, "Operation completed") {
		t.Errorf("expected 'Operation completed' log, got: %s", output)
	}
	if !strings.Contains(output, "test-operation") {
		t.Errorf("expected operation name in log, got: %s", output)
	}
	if !strings.Contains(output, "test-component") {
		t.Errorf("expected component name in log, got: %s", output)
	}
	if !strings.Contains(output, "foo") || !strings.Contains(output, "bar") {
		t.Errorf("expected custom field in log, got: %s", output)
	}
	if !strings.Contains(output, "inside operation") {
		t.Errorf("expected inner log message, got: %s", output)
	}
}

func TestOperationLogger_WithOperation_Error(t *testing.T) {
	teardown := setupTracerProvider(t)
	defer teardown()

	var buf bytes.Buffer
	config := &Config{
		Level:  LevelDebug,
		Format: FormatJSON,
		Output: &buf,
	}
	log := New(config)
	opLogger := NewOperationLogger(log, "test-tracer")
	ctx := context.Background()
	testErr := errors.New("fail")
	err := opLogger.WithOperation(ctx, OperationConfig{
		Name: "fail-operation",
	}, func(ctx context.Context) error {
		return testErr
	})
	if err == nil {
		t.Fatalf("expected error, got nil")
	}
	output := buf.String()
	if !strings.Contains(output, "Operation failed") {
		t.Errorf("expected 'Operation failed' log, got: %s", output)
	}
	if !strings.Contains(output, "fail-operation") {
		t.Errorf("expected operation name in log, got: %s", output)
	}
	if !strings.Contains(output, "fail") {
		t.Errorf("expected error message in log, got: %s", output)
	}
}

func TestOperationLogger_LoggedOperation(t *testing.T) {
	teardown := setupTracerProvider(t)
	defer teardown()

	var buf bytes.Buffer
	config := &Config{
		Level:  LevelDebug,
		Format: FormatJSON,
		Output: &buf,
	}
	log := New(config)
	opLogger := NewOperationLogger(log, "test-tracer")
	ctx := context.Background()

	err := opLogger.LoggedOperation(ctx, "simple-op", func(ctx context.Context) error {
		return nil
	})
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}
	output := buf.String()
	if !strings.Contains(output, "simple-op") {
		t.Errorf("expected operation name in log, got: %s", output)
	}
}

func TestOperationLogger_ComponentOperation(t *testing.T) {
	teardown := setupTracerProvider(t)
	defer teardown()

	var buf bytes.Buffer
	config := &Config{
		Level:  LevelDebug,
		Format: FormatJSON,
		Output: &buf,
	}
	log := New(config)
	opLogger := NewOperationLogger(log, "test-tracer")
	ctx := context.Background()

	err := opLogger.ComponentOperation(ctx, "comp", "op", func(ctx context.Context) error {
		return nil
	})
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}
	output := buf.String()
	if !strings.Contains(output, "comp") {
		t.Errorf("expected component name in log, got: %s", output)
	}
	if !strings.Contains(output, "op") {
		t.Errorf("expected operation name in log, got: %s", output)
	}
}
