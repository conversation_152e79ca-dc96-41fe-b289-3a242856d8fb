// Package logger provides context-aware structured logging with OpenTelemetry integration.
//
// This package is designed specifically for the Resumatter application but can be used
// in any Go application that needs structured logging with trace correlation.
//
// Key Features:
//   - Context-aware logging with automatic trace/span ID extraction
//   - Structured logging with JSON and text output formats
//   - Level-based filtering (Debug, Info, Warn, Error)
//   - Named loggers for component identification
//   - Persistent fields for logger instances
//   - Operation middleware for automatic operation logging and tracing
//   - Zero-allocation field creation for performance
//
// Basic Usage:
//
//	log := logger.New(&logger.Config{
//		Level:  logger.LevelInfo,
//		Format: logger.FormatJSON,
//	})
//
//	ctx := logger.WithLogger(context.Background(), log)
//	logger.Info(ctx, "Operation started", logger.String("op", "tailor"))
//
// OpenTelemetry Integration:
//
// The logger automatically extracts trace and span IDs from the context when
// OpenTelemetry spans are active, providing automatic correlation between
// logs and traces.
//
//	tracer := otel.Tracer("app")
//	ctx, span := tracer.Start(ctx, "operation")
//	defer span.End()
//
//	// This log will include trace_id and span_id
//	logger.Info(ctx, "Processing request")
//
// Operation Middleware:
//
// For complex operations that need both logging and tracing:
//
//	opLogger := logger.NewOperationLogger(log, "app")
//	err := opLogger.WithOperation(ctx, logger.OperationConfig{
//		Name: "process_request",
//		Component: "service",
//	}, func(ctx context.Context) error {
//		// Your operation code here
//		return processRequest(ctx)
//	})
package logger
