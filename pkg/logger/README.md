# Context-Aware Logger with OpenTelemetry Integration

A structured logging package designed for the Resumatter application with built-in OpenTelemetry trace/span ID correlation.

## Features

- **Context-aware logging** - Automatically extracts trace/span IDs from context
- **Structured logging** - JSON and text output formats
- **Level-based filtering** - Debug, Info, Warn, Error levels
- **OpenTelemetry integration** - Automatic trace correlation
- **Operation middleware** - Structured operation logging with tracing
- **Zero-allocation field creation** - Efficient structured fields
- **Named loggers** - Component-specific logging

## Quick Start

```go
import "resumatter/pkg/logger"

// Create a logger
log := logger.New(&logger.Config{
    Level:  logger.LevelInfo,
    Format: logger.FormatJSON,
})

// Use with context
ctx := logger.WithLogger(context.Background(), log)
logger.Info(ctx, "Operation started", 
    logger.String("operation", "tailor_resume"),
    logger.Int("timeout", 30),
)
```

## Configuration

```go
config := &logger.Config{
    Level:      logger.LevelInfo,        // Minimum log level
    Format:     logger.FormatJSON,       // JSON or Text
    Output:     os.Stderr,               // Output destination
    TimeFormat: time.RFC3339,            // Timestamp format
}
```

## Usage Patterns

### Basic Logging

```go
ctx := context.Background()
log := logger.NewDefault()

log.Info(ctx, "User action", 
    logger.String("user_id", "123"),
    logger.String("action", "upload_resume"),
)

log.ErrorWithErr(ctx, "Operation failed", err,
    logger.String("operation", "ai_call"),
    logger.Duration("elapsed", time.Since(start)),
)
```

### Context-Based Logging

```go
// Add logger to context
ctx = logger.WithLogger(ctx, log.Named("service"))

// Use from context anywhere
logger.Info(ctx, "Processing request")
logger.Error(ctx, "Request failed")
```

### Operation Middleware

The `OperationLogger` provides structured logging for operations with automatic OpenTelemetry tracing. It offers three methods for different use cases:

#### Full Configuration with WithOperation

```go
import "go.opentelemetry.io/otel/attribute"

opLogger := logger.NewOperationLogger(log, "resumatter")

err := opLogger.WithOperation(ctx, logger.OperationConfig{
    Name:      "tailor_resume",
    Component: "ai_service",
    Fields: []logger.Field{
        logger.String("model", "gemini-2.0-flash-lite"),
        logger.String("version", "v1.0"),
    },
    Attributes: []attribute.KeyValue{
        attribute.String("service.component", "ai_service"),
    },
    SkipLogging: false, // Set to true to disable automatic start/completion logs
}, func(ctx context.Context) error {
    // Your operation code here
    logger.Info(ctx, "Calling AI service")
    return aiService.TailorResume(ctx, input)
})
```

#### Simple Operations with LoggedOperation

For simple operations that only need a name:

```go
opLogger := logger.NewOperationLogger(log, "resumatter")

err := opLogger.LoggedOperation(ctx, "simple_operation", func(ctx context.Context) error {
    // Your operation code here
    logger.Info(ctx, "Processing data")
    return processData(ctx)
})
```

#### Component-Specific Operations with ComponentOperation

For operations that belong to a specific component:

```go
opLogger := logger.NewOperationLogger(log, "resumatter")

err := opLogger.ComponentOperation(ctx, "ai_service", "generate_summary", func(ctx context.Context) error {
    // Your operation code here
    logger.Info(ctx, "Generating summary")
    return generateSummary(ctx)
})
```

All three methods automatically:
- Create OpenTelemetry spans with the operation name
- Log operation start and completion (unless `SkipLogging` is true)
- Measure and log operation duration
- Handle errors by logging them and recording them in the span
- Add the logger to the context for use within the operation

#### OperationConfig Fields

```go
type OperationConfig struct {
    Name        string                    // Required: Operation name (used for span name)
    Component   string                    // Optional: Component name (creates named logger)
    Fields      []Field                   // Optional: Persistent fields for all logs
    Attributes  []attribute.KeyValue      // Optional: OpenTelemetry span attributes
    SkipLogging bool                      // Optional: Skip automatic start/completion logs
}
```

- **Name**: The operation name, used as the OpenTelemetry span name and included in logs
- **Component**: Creates a named logger (e.g., "ai_service" becomes `logger.Named("ai_service")`)
- **Fields**: Structured fields that will be included in all logs within the operation
- **Attributes**: OpenTelemetry attributes added to the span (separate from log fields)
- **SkipLogging**: Set to `true` to disable automatic "Operation started" and "Operation completed" logs

### Named Loggers

```go
// Create component-specific loggers
serviceLogger := log.Named("service")
aiLogger := serviceLogger.Named("ai_client")
httpLogger := serviceLogger.Named("http_server")

// Each maintains its hierarchy
aiLogger.Info(ctx, "API call") // logs as [service.ai_client]
```

### Persistent Fields

```go
// Add fields that persist across log calls
userLogger := log.With(
    logger.String("user_id", "123"),
    logger.String("session_id", "abc"),
)

userLogger.Info(ctx, "Action 1") // includes user_id and session_id
userLogger.Info(ctx, "Action 2") // includes user_id and session_id
```

## OpenTelemetry Integration

The logger automatically extracts trace and span IDs from the context when OpenTelemetry spans are active:

```go
tracer := otel.Tracer("resumatter")
ctx, span := tracer.Start(ctx, "tailor-resume")
defer span.End()

// This log will include trace_id and span_id
logger.Info(ctx, "Processing in span")
```

Output:
```json
{
  "timestamp": "2025-07-15T10:30:00Z",
  "level": "INFO",
  "message": "Processing in span",
  "trace_id": "4bf92f3577b34da6a3ce929d0e0e4736",
  "span_id": "00f067aa0ba902b7"
}
```

## Output Formats

### JSON Format
```json
{
  "timestamp": "2025-07-15T10:30:00Z",
  "level": "INFO",
  "message": "Operation completed",
  "logger": "service.ai_client",
  "trace_id": "4bf92f3577b34da6a3ce929d0e0e4736",
  "span_id": "00f067aa0ba902b7",
  "fields": {
    "operation": "tailor_resume",
    "duration": "150ms",
    "tokens": 1500
  }
}
```

### Text Format
```
2025-07-15T10:30:00Z [INFO] [service.ai_client] Operation completed trace_id=4bf92f3577b34da6a3ce929d0e0e4736 span_id=00f067aa0ba902b7 operation=tailor_resume duration=150ms tokens=1500
```

## Field Types

```go
logger.String("key", "value")
logger.Int("count", 42)
logger.Duration("elapsed", time.Since(start))
logger.Error(err)
logger.Any("data", complexObject)
```

## Best Practices

### 1. Use Structured Fields
```go
// Good
logger.Info(ctx, "User login", 
    logger.String("user_id", userID),
    logger.String("method", "oauth"),
)

// Avoid
logger.Info(ctx, fmt.Sprintf("User %s logged in via %s", userID, method))
```

### 2. Consistent Field Names
```go
// Use consistent field names across the application
logger.String("user_id", "123")     // not "userId" or "user"
logger.String("request_id", "abc")  // not "reqId" or "request"
logger.Duration("duration", dur)    // not "elapsed" or "time"
```

### 3. Appropriate Log Levels
```go
logger.Debug(ctx, "Detailed debugging info")     // Development only
logger.Info(ctx, "Normal operation events")      // Production events
logger.Warn(ctx, "Unusual but handled events")   // Potential issues
logger.Error(ctx, "Error conditions")            // Actual problems
```

### 4. Error Logging
```go
// Include context with errors
logger.ErrorWithErr(ctx, "Failed to process request", err,
    logger.String("user_id", userID),
    logger.String("operation", "tailor_resume"),
    logger.String("error_code", "AI_SERVICE_TIMEOUT"),
)
```

## Integration with Resumatter

### Service Layer Integration
```go
// In pkg/service/service.go
func (s *Service) TailorResume(ctx context.Context, resumeFile, jobFile string) (*types.TailorResumeOutput, error) {
    log := logger.FromContext(ctx).Named("service")
    
    log.Info(ctx, "Starting resume tailoring",
        logger.String("resume_file", resumeFile),
        logger.String("job_file", jobFile),
    )
    
    // ... existing code ...
    
    if err != nil {
        log.ErrorWithErr(ctx, "Failed to tailor resume", err,
            logger.String("step", "ai_call"),
        )
        return nil, err
    }
    
    log.Info(ctx, "Resume tailoring completed",
        logger.Int("ats_score", result.ATSAnalysis.Score),
    )
    
    return result, nil
}
```

### CLI Integration
```go
// In internal/cli/tailor.go
func (a *App) runTailor(ctx context.Context, resumeFile, jobFile, outputFile, outputFormat string) error {
    log := logger.New(&logger.Config{
        Level:  logger.LevelInfo,
        Format: logger.FormatText, // Human-readable for CLI
    }).Named("cli")

    ctx = logger.WithLogger(ctx, log)

    opLogger := logger.NewOperationLogger(log, "resumatter")

    // Full configuration approach
    return opLogger.WithOperation(ctx, logger.OperationConfig{
        Name: "tailor_resume_command",
        Fields: []logger.Field{
            logger.String("resume_file", resumeFile),
            logger.String("job_file", jobFile),
            logger.String("output_format", outputFormat),
        },
    }, func(ctx context.Context) error {
        return a.service.TailorResume(ctx, resumeFile, jobFile)
    })

    // Alternative: Simple approach for basic operations
    // return opLogger.LoggedOperation(ctx, "tailor_resume_command", func(ctx context.Context) error {
    //     return a.service.TailorResume(ctx, resumeFile, jobFile)
    // })
}
```

## Testing

The package includes comprehensive tests covering:
- Level filtering
- Output formatting (JSON/Text)
- OpenTelemetry integration
- Context propagation
- Field handling
- Error scenarios
- Operation middleware functionality

### Testing Operation Middleware

When testing code that uses `OperationLogger`, you can capture logs and verify behavior:

```go
import (
    "bytes"
    "context"
    "testing"

    "go.opentelemetry.io/otel"
    otelstdout "go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
    oteltrace "go.opentelemetry.io/otel/sdk/trace"
    "github.com/stretchr/testify/assert"
)

func TestMyOperation(t *testing.T) {
    // Setup tracer provider for testing
    exp, _ := otelstdout.New(otelstdout.WithPrettyPrint())
    tp := oteltrace.NewTracerProvider(oteltrace.WithBatcher(exp))
    otel.SetTracerProvider(tp)
    defer tp.Shutdown(context.Background())

    // Capture logs
    var buf bytes.Buffer
    config := &logger.Config{
        Level:  logger.LevelDebug,
        Format: logger.FormatJSON,
        Output: &buf,
    }
    log := logger.New(config)
    opLogger := logger.NewOperationLogger(log, "test-tracer")

    // Test operation
    err := opLogger.WithOperation(context.Background(), logger.OperationConfig{
        Name:      "test-operation",
        Component: "test-component",
        Fields:    []logger.Field{logger.String("test", "value")},
    }, func(ctx context.Context) error {
        logger.Info(ctx, "Operation running")
        return nil
    })

    // Verify results
    assert.NoError(t, err)
    output := buf.String()
    assert.Contains(t, output, "Operation started")
    assert.Contains(t, output, "Operation completed")
    assert.Contains(t, output, "test-operation")
}
```

Run tests:
```bash
go test ./pkg/logger/...
```

## Dependencies

- `go.opentelemetry.io/otel/trace` - For trace/span ID extraction
- Standard library only for core functionality