package config

import (
	"os"
	"strconv"
	"strings"
	"time"

	"resumatter/pkg/logger"
)

const (
	defaultTemperature = 0.7
	defaultTimeout     = 30 * time.Second
	defaultModel       = "gemini-2.0-flash-lite"
)

// Config holds the application configuration
type Config struct {
	APIKey      string
	Model       string
	Temperature float32
	Timeout     time.Duration

	// Vertex AI configuration
	UseVertexAI        bool
	GoogleCloudProject string
	GoogleCloudRegion  string

	// Logger configuration
	LogLevel  logger.Level
	LogFormat logger.Format

	// Observability configuration
	TracingEnabled bool
	ServiceName    string
	ServiceVersion string
	Environment    string
}

// Load loads configuration from environment variables with sensible defaults
func Load() *Config {
	// Check if Vertex AI should be used
	useVertexAI := false
	if vertexAIEnv := os.Getenv("GOOGLE_GENAI_USE_VERTEXAI"); vertexAIEnv != "" {
		if val, err := strconv.ParseBool(vertexAIEnv); err == nil {
			useVertexAI = val
		}
	}

	apiKey := ""
	if !useVertexAI {
		// Only load API key for non-Vertex AI mode
		apiKey = os.Getenv("GEMINI_API_KEY")
		if apiKey == "" {
			apiKey = os.Getenv("RESUMATTER_AI_APIKEY")
		}
	}

	model := os.Getenv("GEMINI_MODEL")
	if model == "" {
		model = defaultModel
	}

	// Vertex AI specific configuration
	googleCloudProject := os.Getenv("GOOGLE_CLOUD_PROJECT")
	googleCloudRegion := os.Getenv("GOOGLE_CLOUD_LOCATION")
	if googleCloudRegion == "" {
		googleCloudRegion = os.Getenv("GOOGLE_CLOUD_REGION")
	}

	// Logger configuration
	logLevel := parseLogLevel(os.Getenv("LOG_LEVEL"))
	logFormat := parseLogFormat(os.Getenv("LOG_FORMAT"))

	// Observability configuration
	tracingEnabled := false
	if tracingEnv := os.Getenv("OTEL_TRACING_ENABLED"); tracingEnv != "" {
		if val, err := strconv.ParseBool(tracingEnv); err == nil {
			tracingEnabled = val
		}
	}

	serviceName := os.Getenv("OTEL_SERVICE_NAME")
	if serviceName == "" {
		serviceName = "resumatter"
	}

	serviceVersion := os.Getenv("OTEL_SERVICE_VERSION")
	if serviceVersion == "" {
		serviceVersion = "dev"
	}

	environment := os.Getenv("OTEL_ENVIRONMENT")
	if environment == "" {
		environment = "development"
	}

	return &Config{
		APIKey:             apiKey,
		Model:              model,
		Temperature:        defaultTemperature,
		Timeout:            defaultTimeout,
		UseVertexAI:        useVertexAI,
		GoogleCloudProject: googleCloudProject,
		GoogleCloudRegion:  googleCloudRegion,
		LogLevel:           logLevel,
		LogFormat:          logFormat,
		TracingEnabled:     tracingEnabled,
		ServiceName:        serviceName,
		ServiceVersion:     serviceVersion,
		Environment:        environment,
	}
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.UseVertexAI {
		// For Vertex AI, we need project and region
		if c.GoogleCloudProject == "" {
			return ErrMissingGoogleCloudProject
		}
		if c.GoogleCloudRegion == "" {
			return ErrMissingGoogleCloudRegion
		}
	} else {
		// For regular API, we need API key
		if c.APIKey == "" {
			return ErrMissingAPIKey
		}
	}
	return nil
}

// parseLogLevel parses the log level from environment variable
func parseLogLevel(level string) logger.Level {
	switch strings.ToUpper(level) {
	case "DEBUG":
		return logger.LevelDebug
	case "INFO":
		return logger.LevelInfo
	case "WARN", "WARNING":
		return logger.LevelWarn
	case "ERROR":
		return logger.LevelError
	default:
		return logger.LevelInfo // default to info
	}
}

// parseLogFormat parses the log format from environment variable
func parseLogFormat(format string) logger.Format {
	switch strings.ToLower(format) {
	case "json":
		return logger.FormatJSON
	case "text":
		return logger.FormatText
	default:
		return logger.FormatText // default to text
	}
}
