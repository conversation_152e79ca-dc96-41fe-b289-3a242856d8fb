//go:build fast

package config

import (
	"os"
	"testing"
	"time"
)

func TestLoad(t *testing.T) {
	tests := []struct {
		name        string
		envVars     map[string]string
		expected    *Config
		description string
	}{
		{
			name: "default values",
			envVars: map[string]string{
				"GEMINI_API_KEY": "",
				"GEMINI_MODEL":   "",
			},
			expected: &Config{
				APIKey:      "",
				Model:       "gemini-2.0-flash-lite",
				Temperature: 0.7,
				Timeout:     30 * time.Second,
			},
			description: "should use default values when env vars are not set",
		},
		{
			name: "custom values",
			envVars: map[string]string{
				"GEMINI_API_KEY": "test-api-key",
				"GEMINI_MODEL":   "gemini-1.5-pro",
			},
			expected: &Config{
				APIKey:      "test-api-key",
				Model:       "gemini-1.5-pro",
				Temperature: 0.7,
				Timeout:     30 * time.Second,
			},
			description: "should use custom values from env vars",
		},
		{
			name: "alternative api key",
			envVars: map[string]string{
				"RESUMATTER_AI_APIKEY": "alt-api-key",
				"GEMINI_API_KEY":       "",
			},
			expected: &Config{
				APIKey:      "alt-api-key",
				Model:       "gemini-2.0-flash-lite",
				Temperature: 0.7,
				Timeout:     30 * time.Second,
			},
			description: "should use alternative API key env var",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variables
			for key, value := range tt.envVars {
				if value == "" {
					os.Unsetenv(key)
				} else {
					os.Setenv(key, value)
				}
			}

			// Clean up after test
			defer func() {
				for key := range tt.envVars {
					os.Unsetenv(key)
				}
			}()

			config := Load()

			if config.APIKey != tt.expected.APIKey {
				t.Errorf("APIKey = %v, want %v", config.APIKey, tt.expected.APIKey)
			}
			if config.Model != tt.expected.Model {
				t.Errorf("Model = %v, want %v", config.Model, tt.expected.Model)
			}
			if config.Temperature != tt.expected.Temperature {
				t.Errorf("Temperature = %v, want %v", config.Temperature, tt.expected.Temperature)
			}
			if config.Timeout != tt.expected.Timeout {
				t.Errorf("Timeout = %v, want %v", config.Timeout, tt.expected.Timeout)
			}
		})
	}
}

func TestValidate(t *testing.T) {
	tests := []struct {
		name    string
		config  *Config
		wantErr bool
	}{
		{
			name: "valid config",
			config: &Config{
				APIKey: "test-key",
			},
			wantErr: false,
		},
		{
			name: "missing API key",
			config: &Config{
				APIKey: "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
