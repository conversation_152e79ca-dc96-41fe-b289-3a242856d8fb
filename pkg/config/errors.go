package config

import "errors"

var (
	// ErrMissingAPIKey is returned when no API key is configured
	ErrMissingAPIKey = errors.New("api key is required (set GEMINI_API_KEY or RESUMATTER_AI_APIKEY environment variable)")

	// ErrMissingGoogleCloudProject is returned when Google Cloud Project is not configured for Vertex AI
	ErrMissingGoogleCloudProject = errors.New("google cloud project is required for Vertex AI (set GOOGLE_CLOUD_PROJECT environment variable)")

	// ErrMissingGoogleCloudRegion is returned when Google Cloud Region is not configured for Vertex AI
	ErrMissingGoogleCloudRegion = errors.New("google cloud region is required for Vertex AI (set GOOGLE_CLOUD_LOCATION or GOOGLE_CLOUD_REGION environment variable)")
)
