//go:build fast

package formatter

import (
	"strings"
	"testing"

	"resumatter/pkg/types"
)

func TestFormatter_Format(t *testing.T) {
	f := New()

	tailorOutput := &types.TailorResumeOutput{
		TailoredResume: "Test resume content",
		ATSAnalysis: types.ATSAnalysis{
			Score:      85,
			Strengths:  "Good keywords",
			Weaknesses: "Missing skills",
		},
		JobPostingAnalysis: types.JobPostingAnalysis{
			Clarity:     "Clear and concise",
			Inclusivity: "Inclusive language used",
			Quality:     "High quality posting",
		},
	}

	tests := []struct {
		name    string
		data    any
		format  OutputFormat
		wantErr bool
		checks  []string
	}{
		{
			name:    "tailor output as JSON",
			data:    tailorOutput,
			format:  FormatJSON,
			wantErr: false,
			checks:  []string{`"tailoredResume"`, `"atsAnalysis"`, `"score": 85`},
		},
		{
			name:    "tailor output as text",
			data:    tailorOutput,
			format:  FormatText,
			wantErr: false,
			checks:  []string{"=== TAILORED RESUME ===", "=== ATS ANALYSIS ===", "Score: 85/100"},
		},
		{
			name:    "unsupported format",
			data:    tailorOutput,
			format:  "xml",
			wantErr: true,
		},
		{
			name:    "unsupported data type",
			data:    "invalid data",
			format:  FormatText,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := f.Format(tt.data, tt.format)

			if (err != nil) != tt.wantErr {
				t.Errorf("Format() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				for _, check := range tt.checks {
					if !strings.Contains(result, check) {
						t.Errorf("Format() result missing expected content: %s", check)
					}
				}
			}
		})
	}
}

func TestFormatter_FormatEvaluateOutput(t *testing.T) {
	f := New()

	evaluateOutput := &types.EvaluateResumeOutput{
		Summary: "No issues found",
		Findings: []types.EvaluationFinding{
			{
				Type:        "Overclaim",
				Description: "Exaggerated experience",
				Evidence:    "Claims 10 years but resume shows 5",
			},
		},
	}

	result, err := f.Format(evaluateOutput, FormatText)
	if err != nil {
		t.Fatalf("Format() error = %v", err)
	}

	expectedContent := []string{
		"=== RESUME EVALUATION ===",
		"No issues found",
		"1. Overclaim",
		"Description: Exaggerated experience",
		"Evidence: Claims 10 years but resume shows 5",
	}

	for _, content := range expectedContent {
		if !strings.Contains(result, content) {
			t.Errorf("Format() result missing expected content: %s", content)
		}
	}
}

func TestFormatter_FormatAnalyzeOutput(t *testing.T) {
	f := New()

	analyzeOutput := &types.AnalyzeJobOutput{
		JobQualityScore: 75,
		Clarity: types.JobQualityScore{
			Score:        80,
			Analysis:     "Generally clear",
			Improvements: []string{"Add more details", "Clarify requirements"},
		},
		Inclusivity: types.InclusivityAnalysis{
			Score:        90,
			Analysis:     "Very inclusive",
			FlaggedTerms: []string{"rockstar"},
			Suggestions:  []string{"Use 'excellent' instead of 'rockstar'"},
		},
		CandidateAttraction: types.CandidateAttraction{
			Score:      85,
			Strengths:  []string{"Good benefits", "Clear role"},
			Weaknesses: []string{"Vague requirements"},
		},
		MarketCompetitiveness: types.MarketCompetitiveness{
			SalaryTransparency:  "Good - salary range provided",
			RequirementsRealism: "Realistic for the role level",
			IndustryAlignment:   "Well aligned with industry standards",
		},
		Recommendations: []string{"Improve clarity", "Add more benefits"},
	}

	result, err := f.Format(analyzeOutput, FormatText)
	if err != nil {
		t.Fatalf("Format() error = %v", err)
	}

	expectedContent := []string{
		"=== JOB DESCRIPTION ANALYSIS ===",
		"Overall Quality Score: 75/100",
		"=== CLARITY ===",
		"Score: 80/100",
		"=== INCLUSIVITY ===",
		"Score: 90/100",
		"=== CANDIDATE ATTRACTION ===",
		"Score: 85/100",
		"=== MARKET COMPETITIVENESS ===",
		"=== RECOMMENDATIONS ===",
	}

	for _, content := range expectedContent {
		if !strings.Contains(result, content) {
			t.Errorf("Format() result missing expected content: %s", content)
		}
	}
}
