package formatter

import (
	"encoding/json"
	"fmt"
	"strings"

	"resumatter/pkg/types"
)

// OutputFormat represents the supported output formats
type OutputFormat string

const (
	// FormatText represents human-readable text format
	FormatText OutputFormat = "text"
	// FormatJSON represents JSON format
	FormatJSON OutputFormat = "json"
)

// Formatter handles output formatting
type Formatter struct{}

// New creates a new formatter
func New() *Formatter {
	return &Formatter{}
}

// Format formats the given data according to the specified format
func (f *Formatter) Format(data any, format OutputFormat) (string, error) {
	switch format {
	case FormatJSON:
		return f.formatJSON(data)
	case FormatText:
		return f.formatText(data)
	default:
		return "", fmt.Errorf("unsupported format: %s (supported: json, text)", format)
	}
}

// formatJSON formats data as JSON
func (f *Formatter) formatJSON(data any) (string, error) {
	jsonBytes, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("failed to marshal JSON: %w", err)
	}
	return string(jsonBytes), nil
}

// formatText formats data as human-readable text
func (f *Formatter) formatText(data any) (string, error) {
	switch v := data.(type) {
	case *types.TailorResumeOutput:
		return f.formatTailorOutput(*v), nil
	case types.TailorResumeOutput:
		return f.formatTailorOutput(v), nil
	case *types.EvaluateResumeOutput:
		return f.formatEvaluateOutput(*v), nil
	case types.EvaluateResumeOutput:
		return f.formatEvaluateOutput(v), nil
	case *types.AnalyzeJobOutput:
		return f.formatAnalyzeOutput(*v), nil
	case types.AnalyzeJobOutput:
		return f.formatAnalyzeOutput(v), nil
	default:
		return "", fmt.Errorf("unsupported data type for text formatting: %T", data)
	}
}

func (f *Formatter) formatTailorOutput(output types.TailorResumeOutput) string {
	var sb strings.Builder

	sb.WriteString("=== TAILORED RESUME ===\n\n")
	sb.WriteString(output.TailoredResume)
	sb.WriteString("\n\n")

	sb.WriteString("=== ATS ANALYSIS ===\n")
	sb.WriteString(fmt.Sprintf("Score: %d/100\n", output.ATSAnalysis.Score))
	sb.WriteString(fmt.Sprintf("Strengths: %s\n", output.ATSAnalysis.Strengths))
	sb.WriteString(fmt.Sprintf("Weaknesses: %s\n\n", output.ATSAnalysis.Weaknesses))

	sb.WriteString("=== JOB POSTING ANALYSIS ===\n")
	sb.WriteString(fmt.Sprintf("Clarity: %s\n", output.JobPostingAnalysis.Clarity))
	sb.WriteString(fmt.Sprintf("Inclusivity: %s\n", output.JobPostingAnalysis.Inclusivity))
	sb.WriteString(fmt.Sprintf("Quality: %s\n", output.JobPostingAnalysis.Quality))

	return sb.String()
}

func (f *Formatter) formatEvaluateOutput(output types.EvaluateResumeOutput) string {
	var sb strings.Builder

	sb.WriteString("=== RESUME EVALUATION ===\n\n")
	sb.WriteString("Summary:\n")
	sb.WriteString(output.Summary)
	sb.WriteString("\n\n")

	if len(output.Findings) == 0 {
		sb.WriteString("No integrity issues found.\n")
	} else {
		sb.WriteString(fmt.Sprintf("Found %d integrity issue(s):\n\n", len(output.Findings)))
		for i, finding := range output.Findings {
			sb.WriteString(fmt.Sprintf("%d. %s\n", i+1, finding.Type))
			sb.WriteString(fmt.Sprintf("   Description: %s\n", finding.Description))
			sb.WriteString(fmt.Sprintf("   Evidence: %s\n\n", finding.Evidence))
		}
	}

	return sb.String()
}

func (f *Formatter) formatAnalyzeOutput(output types.AnalyzeJobOutput) string {
	var sb strings.Builder

	sb.WriteString("=== JOB DESCRIPTION ANALYSIS ===\n\n")
	sb.WriteString(fmt.Sprintf("Overall Quality Score: %d/100\n\n", output.JobQualityScore))

	sb.WriteString("=== CLARITY ===\n")
	sb.WriteString(fmt.Sprintf("Score: %d/100\n", output.Clarity.Score))
	sb.WriteString(fmt.Sprintf("Analysis: %s\n", output.Clarity.Analysis))
	if len(output.Clarity.Improvements) > 0 {
		sb.WriteString("Improvements:\n")
		for _, improvement := range output.Clarity.Improvements {
			sb.WriteString(fmt.Sprintf("  • %s\n", improvement))
		}
	}
	sb.WriteString("\n")

	sb.WriteString("=== INCLUSIVITY ===\n")
	sb.WriteString(fmt.Sprintf("Score: %d/100\n", output.Inclusivity.Score))
	sb.WriteString(fmt.Sprintf("Analysis: %s\n", output.Inclusivity.Analysis))
	if len(output.Inclusivity.FlaggedTerms) > 0 {
		sb.WriteString("Flagged Terms:\n")
		for _, term := range output.Inclusivity.FlaggedTerms {
			sb.WriteString(fmt.Sprintf("  • %s\n", term))
		}
	}
	if len(output.Inclusivity.Suggestions) > 0 {
		sb.WriteString("Suggestions:\n")
		for _, suggestion := range output.Inclusivity.Suggestions {
			sb.WriteString(fmt.Sprintf("  • %s\n", suggestion))
		}
	}
	sb.WriteString("\n")

	sb.WriteString("=== CANDIDATE ATTRACTION ===\n")
	sb.WriteString(fmt.Sprintf("Score: %d/100\n", output.CandidateAttraction.Score))
	if len(output.CandidateAttraction.Strengths) > 0 {
		sb.WriteString("Strengths:\n")
		for _, strength := range output.CandidateAttraction.Strengths {
			sb.WriteString(fmt.Sprintf("  • %s\n", strength))
		}
	}
	if len(output.CandidateAttraction.Weaknesses) > 0 {
		sb.WriteString("Weaknesses:\n")
		for _, weakness := range output.CandidateAttraction.Weaknesses {
			sb.WriteString(fmt.Sprintf("  • %s\n", weakness))
		}
	}
	sb.WriteString("\n")

	sb.WriteString("=== MARKET COMPETITIVENESS ===\n")
	sb.WriteString(fmt.Sprintf("Salary Transparency: %s\n", output.MarketCompetitiveness.SalaryTransparency))
	sb.WriteString(fmt.Sprintf("Requirements Realism: %s\n", output.MarketCompetitiveness.RequirementsRealism))
	sb.WriteString(fmt.Sprintf("Industry Alignment: %s\n\n", output.MarketCompetitiveness.IndustryAlignment))

	if len(output.Recommendations) > 0 {
		sb.WriteString("=== RECOMMENDATIONS ===\n")
		for i, rec := range output.Recommendations {
			sb.WriteString(fmt.Sprintf("%d. %s\n", i+1, rec))
		}
	}

	return sb.String()
}
