//go:build integration

package service

import (
	"context"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"resumatter/pkg/config"
)

// TestService_TailorResume_Integration tests the TailorResume method with real API
func TestService_TailorResume_Integration(t *testing.T) {
	// Skip if no API key available
	apiKey := getAPIKey()
	if apiKey == "" {
		t.Skip("Skipping TailorResume integration test: no API key available")
	}

	// Create temporary files for testing
	tmpDir := t.TempDir()
	resumeFile := filepath.Join(tmpDir, "resume.txt")
	jobFile := filepath.Join(tmpDir, "job.txt")

	resumeContent := "Software Engineer with 3 years experience in Go and Python"
	jobContent := "Looking for a Go developer with backend experience"

	err := os.WriteFile(resumeFile, []byte(resumeContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create resume file: %v", err)
	}

	err = os.WriteFile(jobFile, []byte(jobContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create job file: %v", err)
	}

	config := &config.Config{
		APIKey:      apiKey,
		Model:       "gemini-2.0-flash-lite",
		Temperature: 0.7,
	}

	service, err := NewWithConfig(config)
	if err != nil {
		t.Fatalf("Failed to create service: %v", err)
	}
	defer service.Close()

	// Read file contents for the option-based method
	resumeContentBytes, err := os.ReadFile(resumeFile)
	if err != nil {
		t.Fatalf("Failed to read resume file: %v", err)
	}
	jobContentBytes, err := os.ReadFile(jobFile)
	if err != nil {
		t.Fatalf("Failed to read job file: %v", err)
	}

	ctx := context.Background()
	result, err := service.TailorResume(ctx,
		WithResumeData(string(resumeContentBytes)),
		WithJobDescriptionData(string(jobContentBytes)))
	if err != nil {
		t.Fatalf("TailorResume() error = %v", err)
	}

	// Validate result structure
	if result == nil {
		t.Fatal("TailorResume() returned nil result")
	}

	if result.TailoredResume == "" {
		t.Error("TailorResume() returned empty tailored resume")
	}

	if result.ATSAnalysis.Score < 0 || result.ATSAnalysis.Score > 100 {
		t.Errorf("TailorResume() ATS score = %d, want 0-100", result.ATSAnalysis.Score)
	}
}

// TestService_EvaluateResume_Integration tests the EvaluateResume method with real API
func TestService_EvaluateResume_Integration(t *testing.T) {
	// Skip if no API key available
	apiKey := getAPIKey()
	if apiKey == "" {
		t.Skip("Skipping EvaluateResume integration test: no API key available")
	}

	// Create temporary files for testing
	tmpDir := t.TempDir()
	baseResumeFile := filepath.Join(tmpDir, "base_resume.txt")
	tailoredResumeFile := filepath.Join(tmpDir, "tailored_resume.txt")

	baseContent := "Software Engineer with 3 years experience in Go and Python"
	tailoredContent := "Senior Software Engineer with 5 years experience in Go, Python, and machine learning"

	err := os.WriteFile(baseResumeFile, []byte(baseContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create base resume file: %v", err)
	}

	err = os.WriteFile(tailoredResumeFile, []byte(tailoredContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create tailored resume file: %v", err)
	}

	config := &config.Config{
		APIKey:      apiKey,
		Model:       "gemini-2.0-flash-lite",
		Temperature: 0.7,
	}

	service, err := NewWithConfig(config)
	if err != nil {
		t.Fatalf("Failed to create service: %v", err)
	}
	defer service.Close()

	// Read file contents for the option-based method
	baseContentBytes, err := os.ReadFile(baseResumeFile)
	if err != nil {
		t.Fatalf("Failed to read base resume file: %v", err)
	}
	tailoredContentBytes, err := os.ReadFile(tailoredResumeFile)
	if err != nil {
		t.Fatalf("Failed to read tailored resume file: %v", err)
	}

	ctx := context.Background()
	result, err := service.EvaluateResume(ctx,
		WithBaseResumeData(string(baseContentBytes)),
		WithTailoredResumeData(string(tailoredContentBytes)))
	if err != nil {
		t.Fatalf("EvaluateResume() error = %v", err)
	}

	// Validate result structure
	if result == nil {
		t.Fatal("EvaluateResume() returned nil result")
	}

	if result.Summary == "" {
		t.Error("EvaluateResume() returned empty summary")
	}

	if result.Findings == nil {
		t.Error("EvaluateResume() returned nil findings")
	}
}

// TestService_AnalyzeJob_Integration tests the AnalyzeJob method with real API
func TestService_AnalyzeJob_Integration(t *testing.T) {
	// Skip if no API key available
	apiKey := getAPIKey()
	if apiKey == "" {
		t.Skip("Skipping AnalyzeJob integration test: no API key available")
	}

	// Create temporary file for testing
	tmpDir := t.TempDir()
	jobFile := filepath.Join(tmpDir, "job.txt")

	jobContent := "We need a rockstar ninja developer who can work 80 hours a week for minimum wage"

	err := os.WriteFile(jobFile, []byte(jobContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create job file: %v", err)
	}

	config := &config.Config{
		APIKey:      apiKey,
		Model:       "gemini-2.0-flash-lite",
		Temperature: 0.7,
	}

	service, err := NewWithConfig(config)
	if err != nil {
		t.Fatalf("Failed to create service: %v", err)
	}
	defer service.Close()

	// Read file content for the option-based method
	jobContentBytes, err := os.ReadFile(jobFile)
	if err != nil {
		t.Fatalf("Failed to read job file: %v", err)
	}

	ctx := context.Background()
	result, err := service.AnalyzeJob(ctx,
		WithJobDescriptionDataForAnalysis(string(jobContentBytes)))
	if err != nil {
		t.Fatalf("AnalyzeJob() error = %v", err)
	}

	// Validate result structure
	if result == nil {
		t.Fatal("AnalyzeJob() returned nil result")
	}

	if result.JobQualityScore < 0 || result.JobQualityScore > 100 {
		t.Errorf("AnalyzeJob() JobQualityScore = %d, want 0-100", result.JobQualityScore)
	}

	if result.Clarity.Score < 0 || result.Clarity.Score > 100 {
		t.Errorf("AnalyzeJob() Clarity.Score = %d, want 0-100", result.Clarity.Score)
	}
}

// getAPIKey reads the API key from file or environment
func getAPIKey() string {
	// Try reading from file first (relative to project root)
	if data, err := os.ReadFile("../../.key/gemini"); err == nil {
		return strings.TrimSpace(string(data))
	}

	// Try from current directory
	if data, err := os.ReadFile(".key/gemini"); err == nil {
		return strings.TrimSpace(string(data))
	}

	// Fall back to environment variables
	if key := os.Getenv("GEMINI_API_KEY"); key != "" {
		return key
	}

	if key := os.Getenv("RESUMATTER_AI_APIKEY"); key != "" {
		return key
	}

	return ""
}
