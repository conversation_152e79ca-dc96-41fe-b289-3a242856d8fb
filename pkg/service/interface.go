package service

import (
	"context"
	"resumatter/pkg/formatter"
	"resumatter/pkg/logger"
	"resumatter/pkg/types"
)

type ServiceInterface interface {
	TailorResume(ctx context.Context, opts ...TailorResumeOption) (*types.TailorResumeOutput, error)
	EvaluateResume(ctx context.Context, opts ...EvaluateResumeOption) (*types.EvaluateResumeOutput, error)
	AnalyzeJob(ctx context.Context, opts ...AnalyzeJobOption) (*types.AnalyzeJobOutput, error)
	WriteOutput(data any, format formatter.OutputFormat, outputFile string) error
	Close() error
	Logger() logger.Logger
}
