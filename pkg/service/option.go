package service

import (
	"resumatter/pkg/fileio"
	"resumatter/pkg/types"
)

type TailorResumeOption func(*types.TailorResumeInput) error
type EvaluateResumeOption func(*types.EvaluateResumeInput) error
type AnalyzeJobOption func(*types.AnalyzeJobInput) error

func WithResumeData(data string) TailorResumeOption {
	return func(input *types.TailorResumeInput) error {
		input.BaseResume = data
		return nil
	}
}

func WithJobDescriptionData(data string) TailorResumeOption {
	return func(input *types.TailorResumeInput) error {
		input.JobDescription = data
		return nil
	}
}

func WithResumeFile(path string, reader *fileio.Reader) TailorResumeOption {
	return func(input *types.TailorResumeInput) error {
		data, err := reader.ReadFile(path)
		if err != nil {
			return err
		}
		input.BaseResume = data
		return nil
	}
}

func WithJobDescriptionFile(path string, reader *fileio.Reader) TailorResumeOption {
	return func(input *types.TailorResumeInput) error {
		data, err := reader.ReadFile(path)
		if err != nil {
			return err
		}
		input.JobDescription = data
		return nil
	}
}

// EvaluateResume options
func WithBaseResumeData(data string) EvaluateResumeOption {
	return func(input *types.EvaluateResumeInput) error {
		input.BaseResume = data
		return nil
	}
}

func WithTailoredResumeData(data string) EvaluateResumeOption {
	return func(input *types.EvaluateResumeInput) error {
		input.TailoredResume = data
		return nil
	}
}

func WithBaseResumeFile(path string, reader *fileio.Reader) EvaluateResumeOption {
	return func(input *types.EvaluateResumeInput) error {
		data, err := reader.ReadFile(path)
		if err != nil {
			return err
		}
		input.BaseResume = data
		return nil
	}
}

func WithTailoredResumeFile(path string, reader *fileio.Reader) EvaluateResumeOption {
	return func(input *types.EvaluateResumeInput) error {
		data, err := reader.ReadFile(path)
		if err != nil {
			return err
		}
		input.TailoredResume = data
		return nil
	}
}

// AnalyzeJob options
func WithJobDescriptionDataForAnalysis(data string) AnalyzeJobOption {
	return func(input *types.AnalyzeJobInput) error {
		input.JobDescription = data
		return nil
	}
}

func WithJobDescriptionFileForAnalysis(path string, reader *fileio.Reader) AnalyzeJobOption {
	return func(input *types.AnalyzeJobInput) error {
		data, err := reader.ReadFile(path)
		if err != nil {
			return err
		}
		input.JobDescription = data
		return nil
	}
}
