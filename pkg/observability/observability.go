package observability

import (
	"context"
	"fmt"
	"os"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/stdout/stdoutmetric"
	"go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	"go.opentelemetry.io/otel/metric"
	sdkmetric "go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
	"go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.34.0"
	oteltrace "go.opentelemetry.io/otel/trace"

	"resumatter/pkg/logger"
)

// Config holds observability configuration
type Config struct {
	// Service identification
	ServiceName    string
	ServiceVersion string
	Environment    string

	// Tracing configuration
	TracingEnabled bool
	TraceExporter  TraceExporter

	// Metrics configuration
	MetricsEnabled  bool
	MetricsExporter MetricsExporter
	MetricsFilePath string

	// Logger configuration
	Logger logger.Logger
}

// TraceExporter defines the type of trace exporter to use
type TraceExporter string

const (
	TraceExporterStdout TraceExporter = "stdout"
	TraceExporterNone   TraceExporter = "none"
	// Future exporters can be added here:
	// TraceExporterJaeger TraceExporter = "jaeger"
	// TraceExporterOTLP   TraceExporter = "otlp"
)

// MetricsExporter defines the type of metrics exporter to use
type MetricsExporter string

const (
	MetricsExporterStdout   MetricsExporter = "stdout"
	MetricsExporterJSONFile MetricsExporter = "jsonfile"
	MetricsExporterNone     MetricsExporter = "none"
)

// DefaultConfig returns a default observability configuration
func DefaultConfig() *Config {
	return &Config{
		ServiceName:     "resumatter",
		ServiceVersion:  "dev",
		Environment:     "development",
		TracingEnabled:  false,
		TraceExporter:   TraceExporterNone,
		MetricsEnabled:  false,
		MetricsExporter: MetricsExporterNone,
		MetricsFilePath: "metrics.json",
	}
}

// Provider manages the observability setup
type Provider struct {
	config         *Config
	tracerProvider *trace.TracerProvider
	meterProvider  *sdkmetric.MeterProvider
	logger         logger.Logger
}

// NewProvider creates a new observability provider
func NewProvider(config *Config) (*Provider, error) {
	if config == nil {
		config = DefaultConfig()
	}

	// Use provided logger or create a default one
	providerLogger := config.Logger
	if providerLogger == nil {
		providerLogger = logger.NewDefault().Named("observability")
	}

	provider := &Provider{
		config: config,
		logger: providerLogger,
	}

	// Initialize tracing if enabled
	if config.TracingEnabled {
		if err := provider.initTracing(); err != nil {
			return nil, fmt.Errorf("failed to initialize tracing: %w", err)
		}
	}

	// Initialize metrics if enabled
	if config.MetricsEnabled {
		if err := provider.initMetrics(); err != nil {
			return nil, fmt.Errorf("failed to initialize metrics: %w", err)
		}
	}

	return provider, nil
}

// initTracing initializes OpenTelemetry tracing
func (p *Provider) initTracing() error {
	ctx := context.Background()
	p.logger.Info(ctx, "Initializing OpenTelemetry tracing",
		logger.String("exporter", string(p.config.TraceExporter)))

	// Create resource
	res, err := p.createResource()
	if err != nil {
		return fmt.Errorf("failed to create resource: %w", err)
	}

	// Create exporter based on configuration
	var exporter trace.SpanExporter
	switch p.config.TraceExporter {
	case TraceExporterStdout:
		exporter, err = p.createStdoutExporter()
	case TraceExporterNone:
		// No exporter, tracing will be a no-op
		return nil
	default:
		return fmt.Errorf("unsupported trace exporter: %s", p.config.TraceExporter)
	}

	if err != nil {
		return fmt.Errorf("failed to create trace exporter: %w", err)
	}

	// Create tracer provider
	p.tracerProvider = trace.NewTracerProvider(
		trace.WithBatcher(exporter),
		trace.WithResource(res),
	)

	// Set global tracer provider
	otel.SetTracerProvider(p.tracerProvider)

	p.logger.Info(ctx, "OpenTelemetry tracing initialized successfully")
	return nil
}

// createResource creates an OpenTelemetry resource with service information
func (p *Provider) createResource() (*resource.Resource, error) {
	return resource.Merge(
		resource.Default(),
		resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName(p.config.ServiceName),
			semconv.ServiceVersion(p.config.ServiceVersion),
			semconv.DeploymentEnvironmentName(p.config.Environment),
		),
	)
}

// createStdoutExporter creates a stdout trace exporter
func (p *Provider) createStdoutExporter() (trace.SpanExporter, error) {
	return stdouttrace.New(
		stdouttrace.WithWriter(os.Stderr),
		stdouttrace.WithPrettyPrint(),
	)
}

// Tracer returns a named tracer
func (p *Provider) Tracer(name string) oteltrace.Tracer {
	if p.tracerProvider == nil {
		// Return no-op tracer if tracing is not initialized
		return otel.Tracer(name)
	}
	return p.tracerProvider.Tracer(name)
}

// Logger returns the provider's logger
func (p *Provider) Logger() logger.Logger {
	return p.logger
}

// Meter returns a named meter
func (p *Provider) Meter(name string) metric.Meter {
	if p.meterProvider == nil {
		// Return no-op meter if metrics is not initialized
		return otel.Meter(name)
	}
	return p.meterProvider.Meter(name)
}

// initMetrics initializes OpenTelemetry metrics
func (p *Provider) initMetrics() error {
	ctx := context.Background()
	p.logger.Info(ctx, "Initializing OpenTelemetry metrics",
		logger.String("exporter", string(p.config.MetricsExporter)))

	// Create resource (reuse from tracing)
	res, err := p.createResource()
	if err != nil {
		return fmt.Errorf("failed to create resource: %w", err)
	}

	// Create exporter based on configuration
	var exporter sdkmetric.Exporter
	switch p.config.MetricsExporter {
	case MetricsExporterStdout:
		exporter, err = p.createStdoutMetricsExporter()
	case MetricsExporterJSONFile:
		exporter, err = p.createJSONFileMetricsExporter()
	case MetricsExporterNone:
		// No exporter, metrics will be a no-op
		return nil
	default:
		return fmt.Errorf("unsupported metrics exporter: %s", p.config.MetricsExporter)
	}

	if err != nil {
		return fmt.Errorf("failed to create metrics exporter: %w", err)
	}

	// Create meter provider
	reader := sdkmetric.NewPeriodicReader(exporter)
	p.meterProvider = sdkmetric.NewMeterProvider(
		sdkmetric.WithReader(reader),
		sdkmetric.WithResource(res),
	)

	// Set global meter provider
	otel.SetMeterProvider(p.meterProvider)

	p.logger.Info(ctx, "OpenTelemetry metrics initialized successfully")
	return nil
}

// createStdoutMetricsExporter creates a stdout metrics exporter
func (p *Provider) createStdoutMetricsExporter() (sdkmetric.Exporter, error) {
	return stdoutmetric.New(
		stdoutmetric.WithWriter(os.Stderr),
		stdoutmetric.WithPrettyPrint(),
	)
}

// createJSONFileMetricsExporter creates a JSON file metrics exporter
func (p *Provider) createJSONFileMetricsExporter() (sdkmetric.Exporter, error) {
	return NewJSONFileExporter(p.config.MetricsFilePath, p.logger)
}

// Shutdown gracefully shuts down the observability provider
func (p *Provider) Shutdown(ctx context.Context) error {
	var errs []error

	// Shutdown metrics provider first
	if p.meterProvider != nil {
		p.logger.Info(ctx, "Shutting down OpenTelemetry meter provider")
		if err := p.meterProvider.Shutdown(ctx); err != nil {
			p.logger.ErrorWithErr(ctx, "Failed to shutdown meter provider", err)
			errs = append(errs, fmt.Errorf("failed to shutdown meter provider: %w", err))
		}
	}

	// Shutdown tracer provider
	if p.tracerProvider != nil {
		p.logger.Info(ctx, "Shutting down OpenTelemetry tracer provider")
		if err := p.tracerProvider.Shutdown(ctx); err != nil {
			p.logger.ErrorWithErr(ctx, "Failed to shutdown tracer provider", err)
			errs = append(errs, fmt.Errorf("failed to shutdown tracer provider: %w", err))
		}
	}

	// Return first error if any occurred
	if len(errs) > 0 {
		return errs[0]
	}
	return nil
}

// WithTracingFromProvider is a helper to create a context with a span using the provider's tracer
func (p *Provider) WithTracing(ctx context.Context, tracerName, operationName string) (context.Context, oteltrace.Span) {
	tracer := p.Tracer(tracerName)
	return tracer.Start(ctx, operationName)
}
