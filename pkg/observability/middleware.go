package observability

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	oteltrace "go.opentelemetry.io/otel/trace"

	"resumatter/pkg/logger"
)

// ServiceMiddleware provides observability middleware for service operations
type ServiceMiddleware struct {
	provider *Provider
	logger   logger.Logger
}

// NewServiceMiddleware creates a new service middleware
func NewServiceMiddleware(provider *Provider) *ServiceMiddleware {
	return &ServiceMiddleware{
		provider: provider,
		logger:   provider.Logger().Named("middleware"),
	}
}

// WrapOperation wraps a service operation with observability
func (m *ServiceMiddleware) WrapOperation(ctx context.Context, operationName string, fn func(context.Context) error) error {
	// Start timing
	start := time.Now()

	// Create span if tracing is enabled
	var span oteltrace.Span
	if m.provider.config.TracingEnabled {
		ctx, span = m.provider.WithTracing(ctx, "service", operationName)
		defer span.End()
	}

	// Add operation info to logger context
	operationLogger := m.logger.With(
		logger.String("operation", operationName),
		logger.String("start_time", start.Format(time.RFC3339)),
	)
	ctx = logger.WithLogger(ctx, operationLogger)

	// Log operation start
	operationLogger.Info(ctx, fmt.Sprintf("Starting %s", operationName))

	// Execute the operation
	err := fn(ctx)

	// Calculate duration
	duration := time.Since(start)

	// Log operation completion
	if err != nil {
		operationLogger.ErrorWithErr(ctx, fmt.Sprintf("Failed %s", operationName), err,
			logger.Duration("duration", duration))

		// Record error in span if tracing is enabled
		if span != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, err.Error())
		}
	} else {
		operationLogger.Info(ctx, fmt.Sprintf("Completed %s", operationName),
			logger.Duration("duration", duration))

		// Mark span as successful if tracing is enabled
		if span != nil {
			span.SetStatus(codes.Ok, "")
		}
	}

	return err
}

// WrapOperationWithResult wraps a service operation that returns a result
func (m *ServiceMiddleware) WrapOperationWithResult(ctx context.Context, operationName string, fn func(context.Context) (any, error)) (any, error) {
	var result any
	err := m.WrapOperation(ctx, operationName, func(ctx context.Context) error {
		var fnErr error
		result, fnErr = fn(ctx)
		return fnErr
	})
	return result, err
}

// AddSpanAttributes adds attributes to the current span if tracing is enabled
func (m *ServiceMiddleware) AddSpanAttributes(ctx context.Context, attributes map[string]any) {
	if !m.provider.config.TracingEnabled {
		return
	}

	span := oteltrace.SpanFromContext(ctx)
	if !span.IsRecording() {
		return
	}

	for key, value := range attributes {
		switch v := value.(type) {
		case string:
			span.SetAttributes(attribute.String(key, v))
		case int:
			span.SetAttributes(attribute.Int64(key, int64(v)))
		case int64:
			span.SetAttributes(attribute.Int64(key, v))
		case float64:
			span.SetAttributes(attribute.Float64(key, v))
		case bool:
			span.SetAttributes(attribute.Bool(key, v))
		default:
			// For other types, convert to string
			span.SetAttributes(attribute.String(key, fmt.Sprintf("%v", v)))
		}
	}
}

// LogWithSpan logs a message and adds it as a span event if tracing is enabled
func (m *ServiceMiddleware) LogWithSpan(ctx context.Context, level logger.Level, message string, fields ...logger.Field) {
	// Get logger from context or use middleware logger
	contextLogger := logger.FromContextOrNil(ctx)
	if contextLogger == nil {
		contextLogger = m.logger
	}

	// Log the message
	switch level {
	case logger.LevelDebug:
		contextLogger.Debug(ctx, message, fields...)
	case logger.LevelInfo:
		contextLogger.Info(ctx, message, fields...)
	case logger.LevelWarn:
		contextLogger.Warn(ctx, message, fields...)
	case logger.LevelError:
		contextLogger.Error(ctx, message, fields...)
	}

	// Add span event if tracing is enabled
	if m.provider.config.TracingEnabled {
		span := oteltrace.SpanFromContext(ctx)
		if span.IsRecording() {
			span.AddEvent(message)
		}
	}
}
