//go:build fast

package observability

import (
	"context"
	"errors"
	"testing"

	"resumatter/pkg/logger"
)

func TestServiceMiddleware_WrapOperation_Success(t *testing.T) {
	provider, err := NewProvider(&Config{
		TracingEnabled: true,
		TraceExporter:  TraceExporterStdout,
		Logger:         logger.NewDefault(),
	})
	if err != nil {
		t.Fatalf("Failed to create provider: %v", err)
	}
	defer func() {
		if err := provider.Shutdown(context.Background()); err != nil {
			t.<PERSON>rrorf("Failed to shutdown provider: %v", err)
		}
	}()

	middleware := NewServiceMiddleware(provider)

	executed := false
	err = middleware.WrapOperation(context.Background(), "test-operation", func(ctx context.Context) error {
		executed = true
		// Verify logger is in context
		contextLogger := logger.FromContextOrNil(ctx)
		if contextLogger == nil {
			t.<PERSON>("Expected logger to be in context")
		}
		return nil
	})

	if err != nil {
		t.<PERSON><PERSON><PERSON>("Expected no error, got: %v", err)
	}
	if !executed {
		t.Error("Expected operation to be executed")
	}
}

func TestServiceMiddleware_WrapOperation_Error(t *testing.T) {
	provider, err := NewProvider(&Config{
		TracingEnabled: true,
		TraceExporter:  TraceExporterStdout,
		Logger:         logger.NewDefault(),
	})
	if err != nil {
		t.Fatalf("Failed to create provider: %v", err)
	}
	defer func() {
		if err := provider.Shutdown(context.Background()); err != nil {
			t.Errorf("Failed to shutdown provider: %v", err)
		}
	}()

	middleware := NewServiceMiddleware(provider)

	expectedErr := errors.New("test error")
	err = middleware.WrapOperation(context.Background(), "test-operation", func(ctx context.Context) error {
		return expectedErr
	})

	if err != expectedErr {
		t.Errorf("Expected error %v, got: %v", expectedErr, err)
	}
}

func TestServiceMiddleware_WrapOperationWithResult(t *testing.T) {
	provider, err := NewProvider(&Config{
		TracingEnabled: true,
		TraceExporter:  TraceExporterStdout,
		Logger:         logger.NewDefault(),
	})
	if err != nil {
		t.Fatalf("Failed to create provider: %v", err)
	}
	defer func() {
		if err := provider.Shutdown(context.Background()); err != nil {
			t.Errorf("Failed to shutdown provider: %v", err)
		}
	}()

	middleware := NewServiceMiddleware(provider)

	expectedResult := "test result"
	result, err := middleware.WrapOperationWithResult(context.Background(), "test-operation", func(ctx context.Context) (any, error) {
		return expectedResult, nil
	})

	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if result.(string) != expectedResult {
		t.Errorf("Expected result %q, got: %q", expectedResult, result)
	}
}

func TestServiceMiddleware_WrapOperationWithResult_Error(t *testing.T) {
	provider, err := NewProvider(&Config{
		TracingEnabled: true,
		TraceExporter:  TraceExporterStdout,
		Logger:         logger.NewDefault(),
	})
	if err != nil {
		t.Fatalf("Failed to create provider: %v", err)
	}
	defer func() {
		if err := provider.Shutdown(context.Background()); err != nil {
			t.Errorf("Failed to shutdown provider: %v", err)
		}
	}()

	middleware := NewServiceMiddleware(provider)

	expectedErr := errors.New("test error")
	result, err := middleware.WrapOperationWithResult(context.Background(), "test-operation", func(ctx context.Context) (any, error) {
		return "", expectedErr
	})

	if err != expectedErr {
		t.Errorf("Expected error %v, got: %v", expectedErr, err)
	}
	if result.(string) != "" {
		t.Errorf("Expected empty result, got: %q", result)
	}
}

func TestServiceMiddleware_AddSpanAttributes(t *testing.T) {
	provider, err := NewProvider(&Config{
		TracingEnabled: true,
		TraceExporter:  TraceExporterStdout,
		Logger:         logger.NewDefault(),
	})
	if err != nil {
		t.Fatalf("Failed to create provider: %v", err)
	}
	defer func() {
		if err := provider.Shutdown(context.Background()); err != nil {
			t.Errorf("Failed to shutdown provider: %v", err)
		}
	}()

	middleware := NewServiceMiddleware(provider)

	// Create a span context
	ctx, span := provider.WithTracing(context.Background(), "test", "test-operation")
	defer span.End()

	// Add attributes
	attributes := map[string]any{
		"string_attr": "test",
		"int_attr":    42,
		"int64_attr":  int64(123),
		"float_attr":  3.14,
		"bool_attr":   true,
		"custom_attr": []string{"a", "b"}, // Should be converted to string
	}

	// This should not panic or error
	middleware.AddSpanAttributes(ctx, attributes)
}

func TestServiceMiddleware_LogWithSpan(t *testing.T) {
	provider, err := NewProvider(&Config{
		TracingEnabled: true,
		TraceExporter:  TraceExporterStdout,
		Logger:         logger.NewDefault(),
	})
	if err != nil {
		t.Fatalf("Failed to create provider: %v", err)
	}
	defer func() {
		if err := provider.Shutdown(context.Background()); err != nil {
			t.Errorf("Failed to shutdown provider: %v", err)
		}
	}()

	middleware := NewServiceMiddleware(provider)

	// Create a span context
	ctx, span := provider.WithTracing(context.Background(), "test", "test-operation")
	defer span.End()

	// Test different log levels
	middleware.LogWithSpan(ctx, logger.LevelDebug, "debug message")
	middleware.LogWithSpan(ctx, logger.LevelInfo, "info message")
	middleware.LogWithSpan(ctx, logger.LevelWarn, "warn message")
	middleware.LogWithSpan(ctx, logger.LevelError, "error message")
}

func TestServiceMiddleware_WithoutTracing(t *testing.T) {
	provider, err := NewProvider(&Config{
		TracingEnabled: false,
		Logger:         logger.NewDefault(),
	})
	if err != nil {
		t.Fatalf("Failed to create provider: %v", err)
	}

	middleware := NewServiceMiddleware(provider)

	// Operations should still work without tracing
	err = middleware.WrapOperation(context.Background(), "test-operation", func(ctx context.Context) error {
		return nil
	})

	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	// Adding span attributes should not panic
	middleware.AddSpanAttributes(context.Background(), map[string]any{"test": "value"})

	// Logging should still work
	middleware.LogWithSpan(context.Background(), logger.LevelInfo, "test message")
}
