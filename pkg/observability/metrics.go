package observability

import (
	"context"

	"go.opentelemetry.io/otel/metric"

	"resumatter/pkg/logger"
)

// MetricsCollector provides high-level metrics collection methods
type MetricsCollector struct {
	provider *Provider
	logger   logger.Logger

	// Business metrics instruments
	resumesTailoredCounter  metric.Int64Counter
	resumesEvaluatedCounter metric.Int64Counter
	jobsAnalyzedCounter     metric.Int64Counter
	atsScoreHist            metric.Float64Histogram
	jobQualityScoreHist     metric.Float64Histogram
}

// NewMetricsCollector creates a new metrics collector
func NewMetricsCollector(provider *Provider) (*MetricsCollector, error) {
	if provider == nil {
		return nil, nil // Return nil if no provider (metrics disabled)
	}

	collector := &MetricsCollector{
		provider: provider,
		logger:   provider.Logger().Named("metrics"),
	}

	// Initialize instruments if metrics are enabled
	if provider.config.MetricsEnabled {
		if err := collector.initInstruments(); err != nil {
			return nil, err
		}
	}

	return collector, nil
}

// initInstruments creates all the metrics instruments
func (m *MetricsCollector) initInstruments() error {
	meter := m.provider.Meter("resumatter")
	var err error

	// Business metrics
	m.resumesTailoredCounter, err = meter.Int64Counter(
		"resumatter_resumes_tailored_total",
		metric.WithDescription("Total number of resumes tailored"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return err
	}

	m.resumesEvaluatedCounter, err = meter.Int64Counter(
		"resumatter_resumes_evaluated_total",
		metric.WithDescription("Total number of resumes evaluated"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return err
	}

	m.jobsAnalyzedCounter, err = meter.Int64Counter(
		"resumatter_jobs_analyzed_total",
		metric.WithDescription("Total number of job descriptions analyzed"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return err
	}

	m.atsScoreHist, err = meter.Float64Histogram(
		"resumatter_ats_score",
		metric.WithDescription("Distribution of ATS scores"),
		metric.WithUnit("1"),
		metric.WithExplicitBucketBoundaries(0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100),
	)
	if err != nil {
		return err
	}

	m.jobQualityScoreHist, err = meter.Float64Histogram(
		"resumatter_job_quality_score",
		metric.WithDescription("Distribution of job quality scores"),
		metric.WithUnit("1"),
		metric.WithExplicitBucketBoundaries(0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100),
	)
	if err != nil {
		return err
	}

	m.logger.Info(context.Background(), "Metrics instruments initialized successfully")
	return nil
}

// Business Metrics Methods

// IncrementResumesTailored increments the counter for tailored resumes
func (m *MetricsCollector) IncrementResumesTailored(ctx context.Context) {
	if m.resumesTailoredCounter != nil {
		m.resumesTailoredCounter.Add(ctx, 1)
	}
}

// IncrementResumesEvaluated increments the counter for evaluated resumes
func (m *MetricsCollector) IncrementResumesEvaluated(ctx context.Context) {
	if m.resumesEvaluatedCounter != nil {
		m.resumesEvaluatedCounter.Add(ctx, 1)
	}
}

// IncrementJobsAnalyzed increments the counter for analyzed jobs
func (m *MetricsCollector) IncrementJobsAnalyzed(ctx context.Context) {
	if m.jobsAnalyzedCounter != nil {
		m.jobsAnalyzedCounter.Add(ctx, 1)
	}
}

// RecordATSScore records an ATS score in the histogram
func (m *MetricsCollector) RecordATSScore(ctx context.Context, score int) {
	if m.atsScoreHist != nil {
		m.atsScoreHist.Record(ctx, float64(score))
	}
}

// RecordJobQualityScore records a job quality score in the histogram
func (m *MetricsCollector) RecordJobQualityScore(ctx context.Context, score int) {
	if m.jobQualityScoreHist != nil {
		m.jobQualityScoreHist.Record(ctx, float64(score))
	}
}
