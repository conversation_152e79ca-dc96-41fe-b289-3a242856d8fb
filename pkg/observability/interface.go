package observability

import (
	"context"

	oteltrace "go.opentelemetry.io/otel/trace"

	"resumatter/pkg/logger"
)

// ProviderInterface defines the interface for observability providers
type ProviderInterface interface {
	// Tracer returns a named tracer
	Tracer(name string) oteltrace.Tracer

	// Logger returns the provider's logger
	Logger() logger.Logger

	// WithTracing creates a context with a span
	WithTracing(ctx context.Context, tracerName, operationName string) (context.Context, oteltrace.Span)

	// Shutdown gracefully shuts down the provider
	Shutdown(ctx context.Context) error
}

// MiddlewareInterface defines the interface for observability middleware
type MiddlewareInterface interface {
	// WrapOperation wraps a service operation with observability
	WrapOperation(ctx context.Context, operationName string, fn func(context.Context) error) error

	// WrapOperationWithResult wraps a service operation that returns a result
	WrapOperationWithResult(ctx context.Context, operationName string, fn func(context.Context) (any, error)) (any, error)

	// AddSpanAttributes adds attributes to the current span
	AddSpanAttributes(ctx context.Context, attributes map[string]any)

	// LogWithSpan logs a message and adds it as a span event
	LogWithSpan(ctx context.Context, level logger.Level, message string, fields ...logger.Field)
}

// Ensure our implementations satisfy the interfaces
var (
	_ ProviderInterface   = (*Provider)(nil)
	_ MiddlewareInterface = (*ServiceMiddleware)(nil)
)
