//go:build fast

package observability

import (
	"context"
	"testing"
	"time"

	"resumatter/pkg/logger"
)

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()

	if config.ServiceName != "resumatter" {
		t.<PERSON><PERSON>("Expected service name 'resumatter', got '%s'", config.ServiceName)
	}
	if config.ServiceVersion != "dev" {
		t.<PERSON>("Expected service version 'dev', got '%s'", config.ServiceVersion)
	}
	if config.Environment != "development" {
		t.<PERSON>("Expected environment 'development', got '%s'", config.Environment)
	}
	if config.TracingEnabled {
		t.Error("Expected tracing to be disabled by default")
	}
	if config.TraceExporter != TraceExporterNone {
		t.Errorf("Expected trace exporter 'none', got '%s'", config.TraceExporter)
	}
}

func TestNewProvider_WithDefaultConfig(t *testing.T) {
	provider, err := NewProvider(nil)
	if err != nil {
		t.Fatalf("Failed to create provider: %v", err)
	}

	if provider.config.ServiceName != "resumatter" {
		t.Errorf("Expected service name 'resumatter', got '%s'", provider.config.ServiceName)
	}

	// Should have a logger
	if provider.logger == nil {
		t.Error("Expected provider to have a logger")
	}

	// Should not have a tracer provider since tracing is disabled
	if provider.tracerProvider != nil {
		t.Error("Expected tracer provider to be nil when tracing is disabled")
	}
}

func TestNewProvider_WithTracingEnabled(t *testing.T) {
	config := &Config{
		ServiceName:    "test-service",
		ServiceVersion: "1.0.0",
		Environment:    "test",
		TracingEnabled: true,
		TraceExporter:  TraceExporterStdout,
		Logger:         logger.NewDefault(),
	}

	provider, err := NewProvider(config)
	if err != nil {
		t.Fatalf("Failed to create provider with tracing: %v", err)
	}

	// Should have a tracer provider since tracing is enabled
	if provider.tracerProvider == nil {
		t.Error("Expected tracer provider to be initialized when tracing is enabled")
	}

	// Test tracer creation
	tracer := provider.Tracer("test-tracer")
	if tracer == nil {
		t.Error("Expected tracer to be created")
	}

	// Test span creation
	ctx := context.Background()
	_, span := provider.WithTracing(ctx, "test-tracer", "test-operation")
	if span == nil {
		t.Error("Expected span to be created")
	}
	span.End()

	// Test shutdown
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := provider.Shutdown(shutdownCtx); err != nil {
		t.Errorf("Failed to shutdown provider: %v", err)
	}
}

func TestNewProvider_WithCustomLogger(t *testing.T) {
	customLogger := logger.NewDefault().Named("custom")
	config := &Config{
		Logger: customLogger,
	}

	provider, err := NewProvider(config)
	if err != nil {
		t.Fatalf("Failed to create provider: %v", err)
	}

	// The provider should use the custom logger as base, but name it "observability"
	// Since we're using the custom logger directly, just verify it's not nil
	if provider.logger == nil {
		t.Error("Expected provider to have a logger")
	}
}

func TestTraceExporterParsing(t *testing.T) {
	tests := []struct {
		input    string
		expected TraceExporter
	}{
		{"stdout", TraceExporterStdout},
		{"STDOUT", TraceExporterStdout},
		{"none", TraceExporterNone},
		{"NONE", TraceExporterNone},
		{"", TraceExporterNone},
		{"unknown", TraceExporterNone},
	}

	for _, test := range tests {
		result := parseTraceExporter(test.input)
		if result != test.expected {
			t.Errorf("parseTraceExporter(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}
}

func TestConfigOptions(t *testing.T) {
	config := NewConfig(
		WithServiceInfo("test-service", "2.0.0", "production"),
		WithTracing(TraceExporterStdout),
		WithMetrics(MetricsExporterStdout),
	)

	if config.ServiceName != "test-service" {
		t.Errorf("Expected service name 'test-service', got '%s'", config.ServiceName)
	}
	if config.ServiceVersion != "2.0.0" {
		t.Errorf("Expected service version '2.0.0', got '%s'", config.ServiceVersion)
	}
	if config.Environment != "production" {
		t.Errorf("Expected environment 'production', got '%s'", config.Environment)
	}
	if !config.TracingEnabled {
		t.Error("Expected tracing to be enabled")
	}
	if config.TraceExporter != TraceExporterStdout {
		t.Errorf("Expected trace exporter 'stdout', got '%s'", config.TraceExporter)
	}
	if !config.MetricsEnabled {
		t.Error("Expected metrics to be enabled")
	}
}
