package observability

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/metric/metricdata"
	"go.opentelemetry.io/otel/sdk/resource"

	"resumatter/pkg/logger"
)

// JSONFileExporter exports metrics to a JSON file
type JSONFileExporter struct {
	filePath string
	logger   logger.Logger
	mu       sync.Mutex
}

// JSONMetricsData represents the structure of exported metrics
type JSONMetricsData struct {
	Timestamp   time.Time      `json:"timestamp"`
	ServiceInfo ServiceInfo    `json:"service_info"`
	Metrics     []JSONMetric   `json:"metrics"`
	Summary     MetricsSummary `json:"summary"`
}

// ServiceInfo contains service identification
type ServiceInfo struct {
	Name        string `json:"name"`
	Version     string `json:"version"`
	Environment string `json:"environment"`
}

// JSONMetric represents a single metric in JSON format
type JSONMetric struct {
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Unit        string         `json:"unit"`
	Type        string         `json:"type"`
	Data        any            `json:"data"`
	Attributes  map[string]any `json:"attributes,omitempty"`
}

// MetricsSummary provides a high-level summary of key metrics
type MetricsSummary struct {
	ResumesTailored    int64   `json:"resumes_tailored"`
	ResumesEvaluated   int64   `json:"resumes_evaluated"`
	JobsAnalyzed       int64   `json:"jobs_analyzed"`
	AvgATSScore        float64 `json:"avg_ats_score,omitempty"`
	AvgJobQualityScore float64 `json:"avg_job_quality_score,omitempty"`
	TotalOperations    int64   `json:"total_operations"`
}

// CounterData represents counter metric data
type CounterData struct {
	Value int64 `json:"value"`
}

// HistogramData represents histogram metric data
type HistogramData struct {
	Count       uint64             `json:"count"`
	Sum         float64            `json:"sum"`
	Min         float64            `json:"min,omitempty"`
	Max         float64            `json:"max,omitempty"`
	Mean        float64            `json:"mean,omitempty"`
	Buckets     []HistogramBucket  `json:"buckets"`
	Percentiles map[string]float64 `json:"percentiles,omitempty"`
}

// HistogramBucket represents a histogram bucket
type HistogramBucket struct {
	UpperBound float64 `json:"upper_bound"`
	Count      uint64  `json:"count"`
}

// NewJSONFileExporter creates a new JSON file exporter
func NewJSONFileExporter(filePath string, logger logger.Logger) (*JSONFileExporter, error) {
	// Ensure directory exists
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create directory %s: %w", dir, err)
	}

	return &JSONFileExporter{
		filePath: filePath,
		logger:   logger,
	}, nil
}

// Temporality returns the temporality for the exporter
func (e *JSONFileExporter) Temporality(kind metric.InstrumentKind) metricdata.Temporality {
	return metricdata.CumulativeTemporality
}

// Aggregation returns the aggregation for the exporter
func (e *JSONFileExporter) Aggregation(kind metric.InstrumentKind) metric.Aggregation {
	switch kind {
	case metric.InstrumentKindCounter, metric.InstrumentKindUpDownCounter, metric.InstrumentKindObservableCounter, metric.InstrumentKindObservableUpDownCounter:
		return metric.AggregationSum{}
	case metric.InstrumentKindHistogram:
		return metric.AggregationExplicitBucketHistogram{
			Boundaries: []float64{0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100},
		}
	default:
		return metric.AggregationDefault{}
	}
}

// Export exports metrics data to JSON file
func (e *JSONFileExporter) Export(ctx context.Context, rm *metricdata.ResourceMetrics) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	// Convert OpenTelemetry metrics to our JSON format
	jsonData := e.convertToJSON(rm)

	// Write to file
	if err := e.writeToFile(jsonData); err != nil {
		e.logger.ErrorWithErr(ctx, "Failed to write metrics to file", err,
			logger.String("file_path", e.filePath))
		return err
	}

	e.logger.Info(ctx, "Metrics exported to JSON file",
		logger.String("file_path", e.filePath),
		logger.Int("metrics_count", len(jsonData.Metrics)))

	return nil
}

// ForceFlush is a no-op for file exporter
func (e *JSONFileExporter) ForceFlush(ctx context.Context) error {
	return nil
}

// Shutdown is a no-op for file exporter
func (e *JSONFileExporter) Shutdown(ctx context.Context) error {
	return nil
}

// convertToJSON converts OpenTelemetry metrics to JSON format
func (e *JSONFileExporter) convertToJSON(rm *metricdata.ResourceMetrics) *JSONMetricsData {
	jsonData := &JSONMetricsData{
		Timestamp: time.Now(),
		ServiceInfo: ServiceInfo{
			Name:        getResourceAttribute(rm.Resource, "service.name"),
			Version:     getResourceAttribute(rm.Resource, "service.version"),
			Environment: getResourceAttribute(rm.Resource, "deployment.environment"),
		},
		Metrics: make([]JSONMetric, 0),
		Summary: MetricsSummary{},
	}

	// Process each scope metric
	for _, sm := range rm.ScopeMetrics {
		for _, m := range sm.Metrics {
			jsonMetric := e.convertMetric(m)
			if jsonMetric != nil {
				jsonData.Metrics = append(jsonData.Metrics, *jsonMetric)

				// Update summary
				e.updateSummary(&jsonData.Summary, m)
			}
		}
	}

	// Calculate derived summary values
	e.finalizeSummary(&jsonData.Summary)

	return jsonData
}

// convertMetric converts a single metric to JSON format
func (e *JSONFileExporter) convertMetric(m metricdata.Metrics) *JSONMetric {
	jsonMetric := &JSONMetric{
		Name:        m.Name,
		Description: m.Description,
		Unit:        m.Unit,
		Attributes:  make(map[string]any),
	}

	switch data := m.Data.(type) {
	case metricdata.Sum[int64]:
		jsonMetric.Type = "counter"
		if len(data.DataPoints) > 0 {
			dp := data.DataPoints[0] // Take first data point
			jsonMetric.Data = CounterData{Value: dp.Value}
			jsonMetric.Attributes = convertAttributes(dp.Attributes)
		}

	case metricdata.Sum[float64]:
		jsonMetric.Type = "counter"
		if len(data.DataPoints) > 0 {
			dp := data.DataPoints[0]
			jsonMetric.Data = CounterData{Value: int64(dp.Value)}
			jsonMetric.Attributes = convertAttributes(dp.Attributes)
		}

	case metricdata.Histogram[int64]:
		jsonMetric.Type = "histogram"
		if len(data.DataPoints) > 0 {
			dp := data.DataPoints[0]
			jsonMetric.Data = e.convertHistogramInt64(dp)
			jsonMetric.Attributes = convertAttributes(dp.Attributes)
		}

	case metricdata.Histogram[float64]:
		jsonMetric.Type = "histogram"
		if len(data.DataPoints) > 0 {
			dp := data.DataPoints[0]
			jsonMetric.Data = e.convertHistogramFloat64(dp)
			jsonMetric.Attributes = convertAttributes(dp.Attributes)
		}

	default:
		// Unsupported metric type
		return nil
	}

	return jsonMetric
}

// convertHistogramFloat64 converts float64 histogram to JSON format
func (e *JSONFileExporter) convertHistogramFloat64(dp metricdata.HistogramDataPoint[float64]) HistogramData {
	histData := HistogramData{
		Count:   dp.Count,
		Sum:     dp.Sum,
		Buckets: make([]HistogramBucket, len(dp.BucketCounts)),
	}

	// Calculate mean
	if dp.Count > 0 {
		histData.Mean = dp.Sum / float64(dp.Count)
	}

	// Convert buckets
	for i, count := range dp.BucketCounts {
		upperBound := float64(100) // Default upper bound
		if i < len(dp.Bounds) {
			upperBound = dp.Bounds[i]
		}
		histData.Buckets[i] = HistogramBucket{
			UpperBound: upperBound,
			Count:      count,
		}
	}

	// Calculate percentiles (simplified approximation)
	if dp.Count > 0 {
		histData.Percentiles = e.calculatePercentiles(dp.Bounds, dp.BucketCounts, dp.Count)
	}

	return histData
}

// convertHistogramInt64 converts int64 histogram to JSON format
func (e *JSONFileExporter) convertHistogramInt64(dp metricdata.HistogramDataPoint[int64]) HistogramData {
	histData := HistogramData{
		Count:   dp.Count,
		Sum:     float64(dp.Sum),
		Buckets: make([]HistogramBucket, len(dp.BucketCounts)),
	}

	// Calculate mean
	if dp.Count > 0 {
		histData.Mean = float64(dp.Sum) / float64(dp.Count)
	}

	// Convert buckets
	for i, count := range dp.BucketCounts {
		upperBound := float64(100) // Default upper bound
		if i < len(dp.Bounds) {
			upperBound = dp.Bounds[i]
		}
		histData.Buckets[i] = HistogramBucket{
			UpperBound: upperBound,
			Count:      count,
		}
	}

	// Calculate percentiles
	if dp.Count > 0 {
		histData.Percentiles = e.calculatePercentiles(dp.Bounds, dp.BucketCounts, dp.Count)
	}

	return histData
}

// calculatePercentiles calculates approximate percentiles from histogram buckets
func (e *JSONFileExporter) calculatePercentiles(bounds []float64, bucketCounts []uint64, totalCount uint64) map[string]float64 {
	percentiles := map[string]float64{}

	// Calculate cumulative counts
	cumulative := make([]uint64, len(bucketCounts))
	cumulative[0] = bucketCounts[0]
	for i := 1; i < len(bucketCounts); i++ {
		cumulative[i] = cumulative[i-1] + bucketCounts[i]
	}

	// Calculate percentiles (50th, 90th, 95th, 99th)
	targets := map[string]float64{
		"p50": 0.50,
		"p90": 0.90,
		"p95": 0.95,
		"p99": 0.99,
	}

	for name, target := range targets {
		targetCount := uint64(float64(totalCount) * target)

		// Find bucket containing the target count
		for i, cum := range cumulative {
			if cum >= targetCount {
				if i < len(bounds) {
					percentiles[name] = bounds[i]
				} else {
					percentiles[name] = 100.0 // Max value
				}
				break
			}
		}
	}

	return percentiles
}

// updateSummary updates the summary with metric data
func (e *JSONFileExporter) updateSummary(summary *MetricsSummary, m metricdata.Metrics) {
	switch m.Name {
	case "resumatter_resumes_tailored_total":
		if sum, ok := m.Data.(metricdata.Sum[int64]); ok && len(sum.DataPoints) > 0 {
			summary.ResumesTailored = sum.DataPoints[0].Value
		}
	case "resumatter_resumes_evaluated_total":
		if sum, ok := m.Data.(metricdata.Sum[int64]); ok && len(sum.DataPoints) > 0 {
			summary.ResumesEvaluated = sum.DataPoints[0].Value
		}
	case "resumatter_jobs_analyzed_total":
		if sum, ok := m.Data.(metricdata.Sum[int64]); ok && len(sum.DataPoints) > 0 {
			summary.JobsAnalyzed = sum.DataPoints[0].Value
		}
	}
}

// finalizeSummary calculates derived summary values
func (e *JSONFileExporter) finalizeSummary(summary *MetricsSummary) {
	summary.TotalOperations = summary.ResumesTailored + summary.ResumesEvaluated + summary.JobsAnalyzed

	// Note: Average scores would need to be calculated from histogram data
	// This is a simplified implementation - in practice, you'd need to maintain
	// running averages or calculate from histogram buckets
}

// writeToFile writes JSON data to file
func (e *JSONFileExporter) writeToFile(data *JSONMetricsData) error {
	file, err := os.Create(e.filePath)
	if err != nil {
		return fmt.Errorf("failed to create file %s: %w", e.filePath, err)
	}
	defer func() {
		if closeErr := file.Close(); closeErr != nil {
			fmt.Fprintf(os.Stderr, "Warning: failed to close file %s: %v\n", e.filePath, closeErr)
		}
	}()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ") // Pretty print

	if err := encoder.Encode(data); err != nil {
		return fmt.Errorf("failed to encode JSON: %w", err)
	}

	return nil
}

// Helper functions

// getResourceAttribute gets an attribute value from resource
func getResourceAttribute(resource *resource.Resource, key string) string {
	for _, attr := range resource.Attributes() {
		if string(attr.Key) == key {
			return attr.Value.AsString()
		}
	}
	return ""
}

// convertAttributes converts OpenTelemetry attributes to map
func convertAttributes(attrs attribute.Set) map[string]any {
	result := make(map[string]any)
	iter := attrs.Iter()
	for iter.Next() {
		attr := iter.Attribute()
		result[string(attr.Key)] = attr.Value.AsInterface()
	}
	return result
}
