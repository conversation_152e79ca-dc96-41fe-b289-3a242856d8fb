# Observability Package

The observability package provides a unified interface for integrating OpenTelemetry tracing, metrics, and structured logging into the resumatter application. It's designed to "play nice" with OpenTelemetry and make observability integration easy for future enhancements.

## Features

- **OpenTelemetry Integration**: Built-in support for distributed tracing
- **Structured Logging**: Integrates with the existing logger package
- **Middleware Support**: Easy-to-use middleware for wrapping service operations
- **Configurable Exporters**: Support for different trace exporters (stdout, with extensibility for OTLP, Jaeger, etc.)
- **Environment-based Configuration**: Load configuration from environment variables
- **Graceful Shutdown**: Proper cleanup of observability resources

## Quick Start

### Basic Usage

```go
import "resumatter/pkg/observability"

// Create observability provider
config := observability.DefaultConfig()
config.TracingEnabled = true
config.TraceExporter = observability.TraceExporterStdout

provider, err := observability.NewProvider(config)
if err != nil {
    log.Fatal(err)
}
defer provider.Shutdown(context.Background())

// Use tracer
tracer := provider.Tracer("my-component")
ctx, span := tracer.Start(context.Background(), "my-operation")
defer span.End()
```

### With Environment Configuration

```go
// Load configuration from environment variables
config := observability.LoadConfigFromEnv()
provider, err := observability.NewProvider(config)
```

### Using Middleware

```go
// Create middleware for service operations
middleware := observability.NewServiceMiddleware(provider)

// Wrap operations with observability
err := middleware.WrapOperation(ctx, "process-resume", func(ctx context.Context) error {
    // Your business logic here
    return processResume(ctx)
})

// Wrap operations that return results
result, err := middleware.WrapOperationWithResult(ctx, "analyze-job", func(ctx context.Context) (*AnalysisResult, error) {
    return analyzeJob(ctx)
})
```

## Configuration

### Environment Variables

- `OTEL_SERVICE_NAME`: Service name (default: "resumatter")
- `OTEL_SERVICE_VERSION`: Service version (default: "dev")
- `OTEL_ENVIRONMENT`: Environment (default: "development")
- `OTEL_TRACING_ENABLED`: Enable tracing (default: false)
- `OTEL_TRACE_EXPORTER`: Trace exporter type ("stdout", "none")
- `OTEL_METRICS_ENABLED`: Enable metrics (default: false, for future use)

### Programmatic Configuration

```go
config := observability.NewConfig(
    observability.WithServiceInfo("resumatter", "1.0.0", "production"),
    observability.WithTracing(observability.TraceExporterStdout),
    observability.WithMetrics(),
    observability.WithLogger(customLogger),
)
```

## Integration with Existing Logger

The observability package is designed to work seamlessly with the existing logger package:

```go
// Logger automatically extracts trace information from context
logger.Info(ctx, "Processing request") // Will include trace_id and span_id

// Middleware adds structured logging
middleware.LogWithSpan(ctx, logger.LevelInfo, "Operation completed")
```

## Trace Exporters

Currently supported exporters:

- **stdout**: Exports traces to stderr (useful for development)
- **none**: Disables tracing

Future exporters can be easily added:
- OTLP (OpenTelemetry Protocol)
- Jaeger
- Zipkin

## Architecture

The package follows a clean architecture with clear interfaces:

- `ProviderInterface`: Main observability provider
- `MiddlewareInterface`: Service operation middleware
- `Config`: Configuration management
- `ServiceMiddleware`: Implementation for wrapping service operations

## Integration with Service Layer

The observability package integrates naturally with the existing service architecture:

```go
// In service initialization
observabilityProvider, err := observability.NewProvider(observabilityConfig)
if err != nil {
    return nil, err
}

middleware := observability.NewServiceMiddleware(observabilityProvider)

// In service methods
func (s *Service) TailorResume(ctx context.Context, opts ...TailorResumeOption) (*types.TailorResumeOutput, error) {
    return middleware.WrapOperationWithResult(ctx, "tailor-resume", func(ctx context.Context) (*types.TailorResumeOutput, error) {
        // Existing business logic
        return s.aiClient.TailorResume(ctx, input)
    })
}
```

## Testing

The package includes comprehensive tests:

```bash
go test ./pkg/observability/...
```

## Future Enhancements

- **Metrics Support**: Add OpenTelemetry metrics integration
- **Additional Exporters**: OTLP, Jaeger, Zipkin support
- **Sampling Configuration**: Configurable trace sampling
- **Resource Detection**: Automatic resource detection for cloud environments
- **Custom Instrumentation**: Helper functions for custom instrumentation