package observability

import (
	"os"
	"strconv"
	"strings"

	"resumatter/pkg/logger"
)

// LoadConfigFromEnv loads observability configuration from environment variables
func LoadConfigFromEnv() *Config {
	config := DefaultConfig()

	// Service identification
	if serviceName := os.Getenv("OTEL_SERVICE_NAME"); serviceName != "" {
		config.ServiceName = serviceName
	}
	if serviceVersion := os.Getenv("OTEL_SERVICE_VERSION"); serviceVersion != "" {
		config.ServiceVersion = serviceVersion
	}
	if environment := os.Getenv("OTEL_ENVIRONMENT"); environment != "" {
		config.Environment = environment
	}

	// Tracing configuration
	if tracingEnabled := os.Getenv("OTEL_TRACING_ENABLED"); tracingEnabled != "" {
		if enabled, err := strconv.ParseBool(tracingEnabled); err == nil {
			config.TracingEnabled = enabled
		}
	}

	if traceExporter := os.Getenv("OTEL_TRACE_EXPORTER"); traceExporter != "" {
		config.TraceExporter = parseTraceExporter(traceExporter)
	}

	// Metrics configuration
	if metricsEnabled := os.Getenv("OTEL_METRICS_ENABLED"); metricsEnabled != "" {
		if enabled, err := strconv.ParseBool(metricsEnabled); err == nil {
			config.MetricsEnabled = enabled
		}
	}

	if metricsExporter := os.Getenv("OTEL_METRICS_EXPORTER"); metricsExporter != "" {
		config.MetricsExporter = parseMetricsExporter(metricsExporter)
	}

	if metricsFilePath := os.Getenv("OTEL_METRICS_FILE_PATH"); metricsFilePath != "" {
		config.MetricsFilePath = metricsFilePath
	}

	return config
}

// parseTraceExporter parses the trace exporter from environment variable
func parseTraceExporter(exporter string) TraceExporter {
	switch strings.ToLower(exporter) {
	case "stdout":
		return TraceExporterStdout
	case "none", "":
		return TraceExporterNone
	default:
		// Default to none for unknown exporters
		return TraceExporterNone
	}
}

// parseMetricsExporter parses the metrics exporter from environment variable
func parseMetricsExporter(exporter string) MetricsExporter {
	switch strings.ToLower(exporter) {
	case "stdout":
		return MetricsExporterStdout
	case "jsonfile", "json":
		return MetricsExporterJSONFile
	case "none", "":
		return MetricsExporterNone
	default:
		// Default to none for unknown exporters
		return MetricsExporterNone
	}
}

// ConfigOption defines a function that configures observability Config
type ConfigOption func(*Config)

// WithServiceInfo sets service identification information
func WithServiceInfo(name, version, environment string) ConfigOption {
	return func(c *Config) {
		c.ServiceName = name
		c.ServiceVersion = version
		c.Environment = environment
	}
}

// WithTracing enables tracing with the specified exporter
func WithTracing(exporter TraceExporter) ConfigOption {
	return func(c *Config) {
		c.TracingEnabled = true
		c.TraceExporter = exporter
	}
}

// WithMetrics enables metrics with the specified exporter
func WithMetrics(exporter MetricsExporter) ConfigOption {
	return func(c *Config) {
		c.MetricsEnabled = true
		c.MetricsExporter = exporter
	}
}

// WithMetricsFile enables JSON file metrics with custom file path
func WithMetricsFile(filePath string) ConfigOption {
	return func(c *Config) {
		c.MetricsEnabled = true
		c.MetricsExporter = MetricsExporterJSONFile
		c.MetricsFilePath = filePath
	}
}

// WithLogger sets a custom logger
func WithLogger(customLogger logger.Logger) ConfigOption {
	return func(c *Config) {
		c.Logger = customLogger
	}
}

// NewConfig creates a new observability configuration with options
func NewConfig(opts ...ConfigOption) *Config {
	config := DefaultConfig()
	for _, opt := range opts {
		opt(config)
	}
	return config
}
