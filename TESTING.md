# Testing Strategy

This document describes the testing approach for Resumatter, which separates fast unit tests from slower integration tests.

## Overview

The project uses Go build tags to separate different types of tests:

- **`fast`** - Unit tests that run quickly without external dependencies
- **`integration`** - Integration tests that require a valid API key and make real API calls

## Test Types

### Fast Unit Tests (`//go:build fast`)

**Purpose:** Quick feedback during development
**Dependencies:** None (no API calls, no network)
**Runtime:** ~4 seconds
**Coverage:** Core logic, validation, formatting, file I/O

**Files:**
- `pkg/ai/client_test.go` - AI client unit tests (schema validation, etc.)
- `pkg/ai/prompts_test.go` - Prompt content validation
- `pkg/config/config_test.go` - Configuration loading and validation
- `pkg/fileio/fileio_test.go` - File I/O operations
- `pkg/formatter/formatter_test.go` - Output formatting
- `pkg/service/service_test.go` - Service layer unit tests
- `internal/cli/root_test.go` - CLI command structure tests

### Integration Tests (`//go:build integration`)

**Purpose:** End-to-end testing with real API
**Dependencies:** Valid GEMINI_API_KEY or RESUMATTER_AI_APIKEY
**Runtime:** ~8 seconds (depends on API response time)
**Coverage:** Real AI interactions, full workflow testing

**Files:**
- `pkg/ai/client_integration_test.go` - Real AI API calls
- `pkg/service/service_integration_test.go` - Service integration tests
- `internal/cli/integration_test.go` - CLI integration tests

## Running Tests

### Quick Development Workflow
```bash
# Fast unit tests only (recommended for development)
make test-fast
# or
go test -tags=fast ./...
```

### Full Test Suite
```bash
# All tests (requires API key for integration tests)
make test
# or manually:
make test-fast
make test-integration
```

### Integration Tests Only
```bash
# Integration tests (requires API key)
make test-integration
# or
go test -tags=integration ./...
```

### With Coverage
```bash
# Fast tests with coverage (generates coverage.out + coverage.html)
make test-coverage

# Integration tests with coverage
make test-coverage-integration
```

## Environment Setup

### For Unit Tests
No setup required - tests run without external dependencies.

### For Integration Tests
Set one of these environment variables:
```bash
export GEMINI_API_KEY="your-api-key"
# or
export RESUMATTER_AI_APIKEY="your-api-key"
```

## CI/CD Strategy

### Development/PR Checks
```bash
make dev  # fmt, vet, lint, test-fast
```

### Full CI Pipeline
```bash
# Stage 1: Fast feedback
make test-fast

# Stage 2: Integration (with API key)
make test-integration
```

## Benefits

1. **Fast Development** - Unit tests provide immediate feedback (~4 seconds)
2. **Cost Effective** - Integration tests only run when needed
3. **Reliable** - Unit tests don't depend on external services
4. **Clear Separation** - Explicit distinction between test types
5. **Better CI/CD** - Can run different test suites at different stages
6. **Single Module** - Simplified dependency management and unified coverage

## Test Coverage

Current coverage by package (fast tests):
- `pkg/fileio`: 100%
- `pkg/formatter`: 94.2%
- `pkg/service`: 87.5%
- `pkg/config`: 76.0%
- `internal/cli`: 40.4%
- `pkg/ai`: 24.0% (unit tests only, integration tests provide full coverage)

## Best Practices

1. **Write fast tests first** - Cover core logic without external dependencies
2. **Use integration tests sparingly** - Only for end-to-end validation
3. **Mock external dependencies** - In unit tests, mock AI client calls
4. **Test error paths** - Ensure both success and failure scenarios are covered
5. **Keep tests focused** - Each test should verify one specific behavior

## Troubleshooting

### "No test files" message
This is normal for packages that only have integration tests when running fast tests, or vice versa.

### Integration tests failing
1. Check API key is set correctly
2. Verify network connectivity
3. Check API rate limits
4. Ensure API key has proper permissions

### Shell issues with `!`
Use the build tag approach instead of negation:
```bash
# Good
go test -tags=fast ./...

# Avoid (shell issues)
go test -tags='!integration' ./...
```