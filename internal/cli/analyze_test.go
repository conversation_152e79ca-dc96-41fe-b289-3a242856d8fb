//go:build fast

package cli

import (
	"context"
	"errors"
	"os"
	"testing"

	"resumatter/pkg/config"
	"resumatter/pkg/formatter"
	"resumatter/pkg/service"
	"resumatter/pkg/types"
)

func TestApp_runAnalyze(t *testing.T) {
	// Create temporary test file
	tmpDir := t.TempDir()
	jobFile := tmpDir + "/job.txt"
	err := os.WriteFile(jobFile, []byte("Test job description content"), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	tests := []struct {
		name         string
		jobFile      string
		outputFile   string
		outputFormat string
		mockService  *MockService
		wantErr      bool
		errContains  string
	}{
		{
			name:         "successful analyze",
			jobFile:      jobFile,
			outputFile:   "",
			outputFormat: "text",
			mockService: &MockService{
				AnalyzeJobFunc: func(ctx context.Context, opts ...service.AnalyzeJobOption) (*types.AnalyzeJobOutput, error) {
					return &types.AnalyzeJobOutput{
						JobQualityScore: 85,
						Clarity: types.JobQualityScore{
							Score:    90,
							Analysis: "Clear and well-structured",
						},
						Inclusivity: types.InclusivityAnalysis{
							Score:        80,
							Analysis:     "Generally inclusive",
							FlaggedTerms: []string{},
							Suggestions:  []string{"Consider more inclusive language"},
						},
					}, nil
				},
			},
			wantErr: false,
		},
		{
			name:         "service error",
			jobFile:      jobFile,
			outputFile:   "",
			outputFormat: "text",
			mockService: &MockService{
				AnalyzeJobFunc: func(ctx context.Context, opts ...service.AnalyzeJobOption) (*types.AnalyzeJobOutput, error) {
					return nil, errors.New("analysis failed")
				},
			},
			wantErr:     true,
			errContains: "analysis failed",
		},
		{
			name:         "write output error",
			jobFile:      jobFile,
			outputFile:   "output.json",
			outputFormat: "json",
			mockService: &MockService{
				AnalyzeJobFunc: func(ctx context.Context, opts ...service.AnalyzeJobOption) (*types.AnalyzeJobOutput, error) {
					return &types.AnalyzeJobOutput{}, nil
				},
				WriteOutputFunc: func(data any, format formatter.OutputFormat, outputFile string) error {
					return errors.New("write failed")
				},
			},
			wantErr:     true,
			errContains: "write failed",
		},
		{
			name:         "json output format",
			jobFile:      jobFile,
			outputFile:   "",
			outputFormat: "json",
			mockService: &MockService{
				AnalyzeJobFunc: func(ctx context.Context, opts ...service.AnalyzeJobOption) (*types.AnalyzeJobOutput, error) {
					return &types.AnalyzeJobOutput{
						JobQualityScore: 75,
						Clarity: types.JobQualityScore{
							Score:        85,
							Analysis:     "Mostly clear",
							Improvements: []string{"Add more details"},
						},
						Inclusivity: types.InclusivityAnalysis{
							Score:        70,
							Analysis:     "Some issues found",
							FlaggedTerms: []string{"rockstar", "ninja"},
							Suggestions:  []string{"Use more inclusive terms"},
						},
						CandidateAttraction: types.CandidateAttraction{
							Score:      80,
							Strengths:  []string{"Good benefits"},
							Weaknesses: []string{"Unclear requirements"},
						},
						MarketCompetitiveness: types.MarketCompetitiveness{
							SalaryTransparency:  "No salary mentioned",
							RequirementsRealism: "Realistic requirements",
							IndustryAlignment:   "Well aligned",
						},
						Recommendations: []string{"Improve clarity", "Add salary range"},
					}, nil
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			app := &App{
				service: tt.mockService,
				config:  &config.Config{},
			}

			err := app.runAnalyze(context.Background(), tt.jobFile, tt.outputFile, tt.outputFormat)

			if tt.wantErr {
				if err == nil {
					t.Errorf("runAnalyze() expected error but got none")
					return
				}
				if tt.errContains != "" && !contains(err.Error(), tt.errContains) {
					t.Errorf("runAnalyze() error = %v, want error containing %v", err, tt.errContains)
				}
			} else {
				if err != nil {
					t.Errorf("runAnalyze() unexpected error = %v", err)
				}
			}
		})
	}
}

func TestApp_analyzeCmd(t *testing.T) {
	app := &App{
		service: &MockService{},
		config:  &config.Config{},
	}

	outputFile := ""
	outputFormat := "text"
	cmd := app.analyzeCmd(&outputFile, &outputFormat)

	// Test command properties
	if cmd.Use != "analyze [job-description-file]" {
		t.Errorf("analyzeCmd() Use = %v, want %v", cmd.Use, "analyze [job-description-file]")
	}

	if cmd.Short != "Analyze a job description for quality and effectiveness" {
		t.Errorf("analyzeCmd() Short = %v, want %v", cmd.Short, "Analyze a job description for quality and effectiveness")
	}

	// Test argument validation
	tests := []struct {
		name    string
		args    []string
		wantErr bool
	}{
		{
			name:    "correct args",
			args:    []string{"job.txt"},
			wantErr: false,
		},
		{
			name:    "too few args",
			args:    []string{},
			wantErr: true,
		},
		{
			name:    "too many args",
			args:    []string{"job.txt", "extra.txt"},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd.SetArgs(tt.args)
			err := cmd.Args(cmd, tt.args)

			if tt.wantErr && err == nil {
				t.Errorf("analyzeCmd() Args validation expected error but got none")
			}
			if !tt.wantErr && err != nil {
				t.Errorf("analyzeCmd() Args validation unexpected error = %v", err)
			}
		})
	}
}
