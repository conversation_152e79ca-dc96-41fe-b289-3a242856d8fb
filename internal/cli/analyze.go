package cli

import (
	"context"
	"fmt"
	"os"

	"github.com/spf13/cobra"

	"resumatter/pkg/service"
)

// analyzeCmd creates the analyze command
func (a *App) analyzeCmd(outputFile, outputFormat *string) *cobra.Command {
	return &cobra.Command{
		Use:   "analyze [job-description-file]",
		Short: "Analyze a job description for quality and effectiveness",
		Long: `Analyze a job description to assess its quality, clarity, inclusivity,
and effectiveness in attracting qualified candidates. Provides actionable
recommendations for improvement.`,
		Args: cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return a.runAnalyze(cmd.Context(), args[0], *outputFile, *outputFormat)
		},
	}
}

// runAnalyze executes the analyze command
func (a *App) runAnalyze(ctx context.Context, jobFile, outputFile, outputFormat string) error {
	fmt.Fprintln(os.Stderr, "Analyzing job description...")

	timeoutCtx, cancel := context.WithTimeout(ctx, a.config.Timeout)
	defer cancel()

	// Read the file content and pass it as data
	content, err := os.ReadFile(jobFile)
	if err != nil {
		return fmt.Errorf("failed to read job description file: %w", err)
	}

	result, err := a.service.AnalyzeJob(timeoutCtx, service.WithJobDescriptionDataForAnalysis(string(content)))
	if err != nil {
		return err
	}

	format := a.parseOutputFormat(outputFormat)
	return a.service.WriteOutput(result, format, outputFile)
}
