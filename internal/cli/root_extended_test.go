//go:build fast

package cli

import (
	"errors"
	"testing"

	"resumatter/pkg/config"
	"resumatter/pkg/formatter"
)

func TestApp_parseOutputFormat(t *testing.T) {
	app := &App{
		service: &MockService{},
		config:  &config.Config{},
	}

	tests := []struct {
		name   string
		format string
		want   formatter.OutputFormat
	}{
		{
			name:   "json format",
			format: "json",
			want:   formatter.FormatJSON,
		},
		{
			name:   "JSON uppercase",
			format: "JSON",
			want:   formatter.FormatJSON,
		},
		{
			name:   "text format",
			format: "text",
			want:   formatter.FormatText,
		},
		{
			name:   "TEXT uppercase",
			format: "TEXT",
			want:   formatter.FormatText,
		},
		{
			name:   "invalid format defaults to text",
			format: "invalid",
			want:   formatter.FormatText,
		},
		{
			name:   "empty format defaults to text",
			format: "",
			want:   formatter.FormatText,
		},
		{
			name:   "mixed case json",
			format: "Json",
			want:   formatter.FormatJSON,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := app.parseOutputFormat(tt.format)
			if got != tt.want {
				t.Errorf("parseOutputFormat() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestApp_rootCmd(t *testing.T) {
	app := &App{
		service: &MockService{},
		config:  &config.Config{},
	}

	cmd := app.rootCmd()

	// Test command properties
	if cmd.Use != "resumatter" {
		t.Errorf("rootCmd() Use = %v, want %v", cmd.Use, "resumatter")
	}

	if cmd.Short != "AI-powered resume optimization tool" {
		t.Errorf("rootCmd() Short = %v, want %v", cmd.Short, "AI-powered resume optimization tool")
	}

	// Test that all subcommands are added
	subcommands := cmd.Commands()

	if len(subcommands) < 3 {
		t.Errorf("rootCmd() should have at least 3 subcommands (tailor, evaluate, analyze), got %d", len(subcommands))
	}

	// Check that our main commands exist
	commandNames := make(map[string]bool)
	for _, subcmd := range subcommands {
		commandNames[subcmd.Name()] = true
	}

	mainCommands := []string{"tailor", "evaluate", "analyze"}
	for _, cmdName := range mainCommands {
		if !commandNames[cmdName] {
			t.Errorf("rootCmd() missing expected subcommand: %s", cmdName)
		}
	}

	// Test persistent flags
	outputFlag := cmd.PersistentFlags().Lookup("output")
	if outputFlag == nil {
		t.Error("rootCmd() should have --output persistent flag")
	}

	formatFlag := cmd.PersistentFlags().Lookup("format")
	if formatFlag == nil {
		t.Error("rootCmd() should have --format persistent flag")
	}

	if formatFlag != nil && formatFlag.DefValue != "text" {
		t.Errorf("rootCmd() --format flag default = %v, want %v", formatFlag.DefValue, "text")
	}
}

func TestApp_Close(t *testing.T) {
	tests := []struct {
		name        string
		mockService *MockService
		wantErr     bool
		errContains string
	}{
		{
			name: "successful close",
			mockService: &MockService{
				CloseFunc: func() error {
					return nil
				},
			},
			wantErr: false,
		},
		{
			name: "close error",
			mockService: &MockService{
				CloseFunc: func() error {
					return errors.New("close failed")
				},
			},
			wantErr:     true,
			errContains: "close failed",
		},
		{
			name: "nil service",
			mockService: &MockService{
				CloseFunc: nil, // This will use the default behavior
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			app := &App{
				service: tt.mockService,
				config:  &config.Config{},
			}

			err := app.Close()

			if tt.wantErr {
				if err == nil {
					t.Errorf("Close() expected error but got none")
					return
				}
				if tt.errContains != "" && !contains(err.Error(), tt.errContains) {
					t.Errorf("Close() error = %v, want error containing %v", err, tt.errContains)
				}
			} else {
				if err != nil {
					t.Errorf("Close() unexpected error = %v", err)
				}
			}
		})
	}
}

func TestApp_Execute_Integration(t *testing.T) {
	// Test that Execute properly calls Close() even on errors
	closeCalled := false
	mockService := &MockService{
		CloseFunc: func() error {
			closeCalled = true
			return nil
		},
	}

	app := &App{
		service: mockService,
		config:  &config.Config{},
	}

	// This will fail because we're not providing valid arguments,
	// but Close() should still be called
	app.Execute()

	if !closeCalled {
		t.Error("Execute() should call Close() even when command execution fails")
	}
}
