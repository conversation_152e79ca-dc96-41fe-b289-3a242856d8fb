//go:build fast

package cli

import (
	"context"
	"errors"
	"os"
	"testing"

	"resumatter/pkg/config"
	"resumatter/pkg/formatter"
	"resumatter/pkg/service"
	"resumatter/pkg/types"
)

func TestApp_runEvaluate(t *testing.T) {
	// Create temporary test files
	tmpDir := t.TempDir()
	baseResumeFile := tmpDir + "/base.txt"
	tailoredResumeFile := tmpDir + "/tailored.txt"
	err := os.WriteFile(baseResumeFile, []byte("Original resume content"), 0644)
	if err != nil {
		t.Fatalf("Failed to create base resume test file: %v", err)
	}
	err = os.WriteFile(tailoredResumeFile, []byte("Tailored resume content"), 0644)
	if err != nil {
		t.Fatalf("Failed to create tailored resume test file: %v", err)
	}

	tests := []struct {
		name               string
		baseResumeFile     string
		tailoredResumeFile string
		outputFile         string
		outputFormat       string
		mockService        *MockService
		wantErr            bool
		errContains        string
	}{
		{
			name:               "successful evaluate",
			baseResumeFile:     baseResumeFile,
			tailoredResumeFile: tailoredResumeFile,
			outputFile:         "",
			outputFormat:       "text",
			mockService: &MockService{
				EvaluateResumeFunc: func(ctx context.Context, opts ...service.EvaluateResumeOption) (*types.EvaluateResumeOutput, error) {
					return &types.EvaluateResumeOutput{
						Summary:  "No issues found",
						Findings: []types.EvaluationFinding{},
					}, nil
				},
			},
			wantErr: false,
		},
		{
			name:               "service error",
			baseResumeFile:     baseResumeFile,
			tailoredResumeFile: tailoredResumeFile,
			outputFile:         "",
			outputFormat:       "text",
			mockService: &MockService{
				EvaluateResumeFunc: func(ctx context.Context, opts ...service.EvaluateResumeOption) (*types.EvaluateResumeOutput, error) {
					return nil, errors.New("evaluation failed")
				},
			},
			wantErr:     true,
			errContains: "evaluation failed",
		},
		{
			name:               "write output error",
			baseResumeFile:     baseResumeFile,
			tailoredResumeFile: tailoredResumeFile,
			outputFile:         "output.json",
			outputFormat:       "json",
			mockService: &MockService{
				EvaluateResumeFunc: func(ctx context.Context, opts ...service.EvaluateResumeOption) (*types.EvaluateResumeOutput, error) {
					return &types.EvaluateResumeOutput{}, nil
				},
				WriteOutputFunc: func(data any, format formatter.OutputFormat, outputFile string) error {
					return errors.New("write failed")
				},
			},
			wantErr:     true,
			errContains: "write failed",
		},
		{
			name:               "json output format",
			baseResumeFile:     baseResumeFile,
			tailoredResumeFile: tailoredResumeFile,
			outputFile:         "",
			outputFormat:       "json",
			mockService: &MockService{
				EvaluateResumeFunc: func(ctx context.Context, opts ...service.EvaluateResumeOption) (*types.EvaluateResumeOutput, error) {
					return &types.EvaluateResumeOutput{
						Summary: "Test summary",
						Findings: []types.EvaluationFinding{
							{
								Type:        "Overclaim",
								Description: "Test finding",
								Evidence:    "Test evidence",
							},
						},
					}, nil
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			app := &App{
				service: tt.mockService,
				config:  &config.Config{},
			}

			err := app.runEvaluate(context.Background(), tt.baseResumeFile, tt.tailoredResumeFile, tt.outputFile, tt.outputFormat)

			if tt.wantErr {
				if err == nil {
					t.Errorf("runEvaluate() expected error but got none")
					return
				}
				if tt.errContains != "" && !contains(err.Error(), tt.errContains) {
					t.Errorf("runEvaluate() error = %v, want error containing %v", err, tt.errContains)
				}
			} else {
				if err != nil {
					t.Errorf("runEvaluate() unexpected error = %v", err)
				}
			}
		})
	}
}

func TestApp_evaluateCmd(t *testing.T) {
	app := &App{
		service: &MockService{},
		config:  &config.Config{},
	}

	outputFile := ""
	outputFormat := "text"
	cmd := app.evaluateCmd(&outputFile, &outputFormat)

	// Test command properties
	if cmd.Use != "evaluate [base-resume-file] [tailored-resume-file]" {
		t.Errorf("evaluateCmd() Use = %v, want %v", cmd.Use, "evaluate [base-resume-file] [tailored-resume-file]")
	}

	if cmd.Short != "Evaluate a tailored resume for accuracy and consistency" {
		t.Errorf("evaluateCmd() Short = %v, want %v", cmd.Short, "Evaluate a tailored resume for accuracy and consistency")
	}

	// Test argument validation
	tests := []struct {
		name    string
		args    []string
		wantErr bool
	}{
		{
			name:    "correct args",
			args:    []string{"base.txt", "tailored.txt"},
			wantErr: false,
		},
		{
			name:    "too few args",
			args:    []string{"base.txt"},
			wantErr: true,
		},
		{
			name:    "too many args",
			args:    []string{"base.txt", "tailored.txt", "extra.txt"},
			wantErr: true,
		},
		{
			name:    "no args",
			args:    []string{},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd.SetArgs(tt.args)
			err := cmd.Args(cmd, tt.args)

			if tt.wantErr && err == nil {
				t.Errorf("evaluateCmd() Args validation expected error but got none")
			}
			if !tt.wantErr && err != nil {
				t.Errorf("evaluateCmd() Args validation unexpected error = %v", err)
			}
		})
	}
}
