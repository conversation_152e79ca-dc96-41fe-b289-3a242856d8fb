//go:build fast

package cli

import (
	"context"
	"errors"
	"os"
	"testing"

	"resumatter/pkg/config"
	"resumatter/pkg/fileio"
	"resumatter/pkg/formatter"
	"resumatter/pkg/logger"
	"resumatter/pkg/service"
	"resumatter/pkg/types"
)

// MockService implements ServiceInterface for testing
type MockService struct {
	TailorResumeFunc   func(ctx context.Context, opts ...service.TailorResumeOption) (*types.TailorResumeOutput, error)
	EvaluateResumeFunc func(ctx context.Context, opts ...service.EvaluateResumeOption) (*types.EvaluateResumeOutput, error)
	AnalyzeJobFunc     func(ctx context.Context, opts ...service.AnalyzeJobOption) (*types.AnalyzeJobOutput, error)
	WriteOutputFunc    func(data any, format formatter.OutputFormat, outputFile string) error
	CloseFunc          func() error
	LoggerFunc         func() logger.Logger
}

func (m *MockService) TailorResume(ctx context.Context, opts ...service.TailorResumeOption) (*types.TailorResumeOutput, error) {
	if m.TailorResumeFunc != nil {
		return m.TailorResumeFunc(ctx, opts...)
	}
	return &types.TailorResumeOutput{}, nil
}

func (m *MockService) EvaluateResume(ctx context.Context, opts ...service.EvaluateResumeOption) (*types.EvaluateResumeOutput, error) {
	if m.EvaluateResumeFunc != nil {
		return m.EvaluateResumeFunc(ctx, opts...)
	}
	return &types.EvaluateResumeOutput{}, nil
}

func (m *MockService) AnalyzeJob(ctx context.Context, opts ...service.AnalyzeJobOption) (*types.AnalyzeJobOutput, error) {
	if m.AnalyzeJobFunc != nil {
		return m.AnalyzeJobFunc(ctx, opts...)
	}
	return &types.AnalyzeJobOutput{}, nil
}

func (m *MockService) WriteOutput(data any, format formatter.OutputFormat, outputFile string) error {
	if m.WriteOutputFunc != nil {
		return m.WriteOutputFunc(data, format, outputFile)
	}
	return nil
}

func (m *MockService) Close() error {
	if m.CloseFunc != nil {
		return m.CloseFunc()
	}
	return nil
}

func (m *MockService) Logger() logger.Logger {
	return logger.New(&logger.Config{
		Level:  logger.LevelInfo,
		Format: logger.FormatText,
	}).Named("mock")
}

func (m *MockService) Reader() *fileio.Reader {
	return fileio.NewReader()
}

func TestApp_runTailor(t *testing.T) {
	// Create temporary test files
	tmpDir := t.TempDir()
	resumeFile := tmpDir + "/resume.txt"
	jobFile := tmpDir + "/job.txt"
	err := os.WriteFile(resumeFile, []byte("Test resume content"), 0644)
	if err != nil {
		t.Fatalf("Failed to create resume test file: %v", err)
	}
	err = os.WriteFile(jobFile, []byte("Test job description content"), 0644)
	if err != nil {
		t.Fatalf("Failed to create job test file: %v", err)
	}

	tests := []struct {
		name         string
		resumeFile   string
		jobFile      string
		outputFile   string
		outputFormat string
		mockService  *MockService
		wantErr      bool
		errContains  string
	}{
		{
			name:         "successful tailor",
			resumeFile:   resumeFile,
			jobFile:      jobFile,
			outputFile:   "",
			outputFormat: "text",
			mockService: &MockService{
				TailorResumeFunc: func(ctx context.Context, opts ...service.TailorResumeOption) (*types.TailorResumeOutput, error) {
					return &types.TailorResumeOutput{
						TailoredResume: "Tailored resume content",
					}, nil
				},
			},
			wantErr: false,
		},
		{
			name:         "service error",
			resumeFile:   resumeFile,
			jobFile:      jobFile,
			outputFile:   "",
			outputFormat: "text",
			mockService: &MockService{
				TailorResumeFunc: func(ctx context.Context, opts ...service.TailorResumeOption) (*types.TailorResumeOutput, error) {
					return nil, errors.New("service error")
				},
			},
			wantErr:     true,
			errContains: "service error",
		},
		{
			name:         "write output error",
			resumeFile:   resumeFile,
			jobFile:      jobFile,
			outputFile:   "output.txt",
			outputFormat: "json",
			mockService: &MockService{
				TailorResumeFunc: func(ctx context.Context, opts ...service.TailorResumeOption) (*types.TailorResumeOutput, error) {
					return &types.TailorResumeOutput{}, nil
				},
				WriteOutputFunc: func(data any, format formatter.OutputFormat, outputFile string) error {
					return errors.New("write error")
				},
			},
			wantErr:     true,
			errContains: "write error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			app := &App{
				service: tt.mockService,
				config:  &config.Config{},
			}

			err := app.runTailor(context.Background(), tt.resumeFile, tt.jobFile, tt.outputFile, tt.outputFormat)

			if tt.wantErr {
				if err == nil {
					t.Errorf("runTailor() expected error but got none")
					return
				}
				if tt.errContains != "" && !contains(err.Error(), tt.errContains) {
					t.Errorf("runTailor() error = %v, want error containing %v", err, tt.errContains)
				}
			} else {
				if err != nil {
					t.Errorf("runTailor() unexpected error = %v", err)
				}
			}
		})
	}
}

func TestApp_tailorCmd(t *testing.T) {
	app := &App{
		service: &MockService{},
		config:  &config.Config{},
	}

	outputFile := ""
	outputFormat := "text"
	cmd := app.tailorCmd(&outputFile, &outputFormat)

	// Test command properties
	if cmd.Use != "tailor [resume-file] [job-description-file]" {
		t.Errorf("tailorCmd() Use = %v, want %v", cmd.Use, "tailor [resume-file] [job-description-file]")
	}

	if cmd.Short != "Tailor a resume for a specific job description" {
		t.Errorf("tailorCmd() Short = %v, want %v", cmd.Short, "Tailor a resume for a specific job description")
	}

	// Test argument validation
	tests := []struct {
		name    string
		args    []string
		wantErr bool
	}{
		{
			name:    "correct args",
			args:    []string{"resume.txt", "job.txt"},
			wantErr: false,
		},
		{
			name:    "too few args",
			args:    []string{"resume.txt"},
			wantErr: true,
		},
		{
			name:    "too many args",
			args:    []string{"resume.txt", "job.txt", "extra.txt"},
			wantErr: true,
		},
		{
			name:    "no args",
			args:    []string{},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmd.SetArgs(tt.args)
			err := cmd.Args(cmd, tt.args)

			if tt.wantErr && err == nil {
				t.Errorf("tailorCmd() Args validation expected error but got none")
			}
			if !tt.wantErr && err != nil {
				t.Errorf("tailorCmd() Args validation unexpected error = %v", err)
			}
		})
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			func() bool {
				for i := 0; i <= len(s)-len(substr); i++ {
					if s[i:i+len(substr)] == substr {
						return true
					}
				}
				return false
			}())))
}
