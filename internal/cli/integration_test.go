//go:build integration

package cli

import (
	"context"
	"os"
	"testing"
)

func TestExecute(t *testing.T) {
	os.Setenv("GEMINI_API_KEY", "test-api-key")
	defer os.Unsetenv("GEMINI_API_KEY")

	tests := []struct {
		name    string
		args    []string
		wantErr bool
	}{
		{
			name:    "help command",
			args:    []string{"--help"},
			wantErr: false,
		},
		{
			name:    "invalid command",
			args:    []string{"invalid-command"},
			wantErr: true,
		},
		{
			name:    "tailor with insufficient args",
			args:    []string{"tailor", "only-one-arg"},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			app, err := NewApp()
			if err != nil {
				t.Fatalf("NewApp() failed: %v", err)
			}

			cmd := app.rootCmd()
			cmd.SetArgs(tt.args)

			err = cmd.Execute()
			if (err != nil) != tt.wantErr {
				t.<PERSON>rrorf("Execute() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestAppExecute(t *testing.T) {
	os.Setenv("GEMINI_API_KEY", "test-api-key")
	defer os.Unsetenv("GEMINI_API_KEY")

	app, err := NewApp()
	if err != nil {
		t.Fatalf("NewApp() failed: %v", err)
	}

	// Set args for help command to avoid actual execution
	cmd := app.rootCmd()
	cmd.SetArgs([]string{"--help"})

	// Test the App's Execute method directly
	err = app.Execute()
	if err != nil {
		t.Errorf("App.Execute() failed: %v", err)
	}
}

func TestRunTailorFileErrors(t *testing.T) {
	os.Setenv("GEMINI_API_KEY", "test-api-key")
	defer os.Unsetenv("GEMINI_API_KEY")

	app, err := NewApp()
	if err != nil {
		t.Fatalf("NewApp() failed: %v", err)
	}

	tests := []struct {
		name       string
		resumeFile string
		jobFile    string
		wantErr    bool
	}{
		{
			name:       "missing resume file",
			resumeFile: "nonexistent-resume.txt",
			jobFile:    "examples/job.txt",
			wantErr:    true,
		},
		{
			name:       "missing job file",
			resumeFile: "examples/resume.txt",
			jobFile:    "nonexistent-job.txt",
			wantErr:    true,
		},
	}
	ctx := context.Background()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := app.runTailor(ctx, tt.resumeFile, tt.jobFile, "", "text")
			if (err != nil) != tt.wantErr {
				t.Errorf("runTailor() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRunEvaluateFileErrors(t *testing.T) {
	os.Setenv("GEMINI_API_KEY", "test-api-key")
	defer os.Unsetenv("GEMINI_API_KEY")

	app, err := NewApp()
	if err != nil {
		t.Fatalf("NewApp() failed: %v", err)
	}

	tests := []struct {
		name               string
		baseResumeFile     string
		tailoredResumeFile string
		wantErr            bool
	}{
		{
			name:               "missing base resume file",
			baseResumeFile:     "nonexistent-base.txt",
			tailoredResumeFile: "examples/resume.txt",
			wantErr:            true,
		},
		{
			name:               "missing tailored resume file",
			baseResumeFile:     "examples/resume.txt",
			tailoredResumeFile: "nonexistent-tailored.txt",
			wantErr:            true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := app.runEvaluate(context.Background(), tt.baseResumeFile, tt.tailoredResumeFile, "", "text")
			if (err != nil) != tt.wantErr {
				t.Errorf("runEvaluate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRunAnalyzeFileErrors(t *testing.T) {
	os.Setenv("GEMINI_API_KEY", "test-api-key")
	defer os.Unsetenv("GEMINI_API_KEY")

	app, err := NewApp()
	if err != nil {
		t.Fatalf("NewApp() failed: %v", err)
	}

	// Test with missing job file
	err = app.runAnalyze(context.Background(), "nonexistent-job.txt", "", "text")
	if err == nil {
		t.Error("runAnalyze() with missing file should error")
	}
}
