package cli

import (
	"context"
	"fmt"
	"os"

	"github.com/spf13/cobra"

	"resumatter/pkg/service"
)

// evaluateCmd creates the evaluate command
func (a *App) evaluateCmd(outputFile, outputFormat *string) *cobra.Command {
	return &cobra.Command{
		Use:   "evaluate [base-resume-file] [tailored-resume-file]",
		Short: "Evaluate a tailored resume for accuracy and consistency",
		Long: `Evaluate a tailored resume against the original to detect potential
fabrications, exaggerations, or inconsistencies. This helps ensure the
tailored resume maintains accuracy while being optimized.`,
		Args: cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			return a.runEvaluate(cmd.Context(), args[0], args[1], *outputFile, *outputFormat)
		},
	}
}

// runEvaluate executes the evaluate command
func (a *App) runEvaluate(ctx context.Context, baseResumeFile, tailoredResumeFile, outputFile, outputFormat string) error {
	fmt.Fprintln(os.Stderr, "Evaluating resume...")

	timeoutCtx, cancel := context.WithTimeout(ctx, a.config.Timeout)
	defer cancel()

	// Read the file contents and pass them as data
	baseResumeContent, err := os.ReadFile(baseResumeFile)
	if err != nil {
		return fmt.Errorf("failed to read base resume file: %w", err)
	}

	tailoredResumeContent, err := os.ReadFile(tailoredResumeFile)
	if err != nil {
		return fmt.Errorf("failed to read tailored resume file: %w", err)
	}

	result, err := a.service.EvaluateResume(timeoutCtx,
		service.WithBaseResumeData(string(baseResumeContent)),
		service.WithTailoredResumeData(string(tailoredResumeContent)))
	if err != nil {
		return err
	}

	format := a.parseOutputFormat(outputFormat)
	return a.service.WriteOutput(result, format, outputFile)
}
