# Reminders

## no emoji
emoji causes error in find-replace tool

## project root dir is /workspace
common mistakes:
`cd /tmp && go mod tidy`
`cd /tmp && go vet`

## modernize: interface{} -> any
`map[string]interface{}` -> `map[string]any`
or simply
`modernize -fix -test ./...`
be careful with your edits after this, source code modified.

## format code
`go fmt ./...`
be careful with your edits after this, source code rearranged.

## lint
`golangci-lint run`
or
`make lint`

## gradual coding
step by step, start simple, then add complexity
`make build`
make sure it compiles before adding more complexity

## quick reference access
`go doc pkgname.ExportedMember`

## package dependencies
`go get pkgname` instead of editing go.mod
or just use it in code then `go mod tidy`

## gemini api key
`GEMINI_API_KEY=$(cat ./.key/gemini) cmd arg ...`

